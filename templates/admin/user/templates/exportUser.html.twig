<div class="modal fade" id="exportModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="exampleModalLabel">{{ 'stats.export.users_export_title'|trans({}, 'messages',  app.user.locale) }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="row">
						<div class="col-12">
							<div class="form-group text-nowrap">
								<label for="filename" class="text-left">{{ 'stats.export.filename' |trans({}, 'messages',  app.user.locale) }}</label>
								<input type="text" v-model="filters.filenameuser" name="filename" class="form-control" id="filename" maxlength="25"/>
							</div>
						</div>
						{% if filters|length > 0 %}
							{% for filter in filters %}
								<div class="col-md-4 col-sm-3 col-xs-12">
									<div class="form-group text-nowrap">
										<label for="country" class="text-left">{{ filter.name }}</label>
										<select id="country" v-model="customFilter['{{ filter.name }}']" name="{{ filter.name }}" class="form-control" data-ea-widget="ea-autocomplete" data-allow-clear="true">
											<option value=""></option>
											{% for lowFilter in filter.filters %}
												<option value="{{ lowFilter.id }}">{{ lowFilter.name }}</option>
											{% endfor %}
										</select>
									</div>
								</div>
							{% endfor %}
						{% endif %}
					</div>
					
					 <div class="row" style="background-color:var(--color-primary-lightest)">
					 		<div class="col-md-12 col-sm-12 col-xs-12">
								<div class="form-group text-wrap">
									<label style="font-weight:600" class="text-left">{{ 'stats.export.user_creation'|trans({}, 'messages',  app.user.locale) }}:</label>
								</div>	
							</div>	
                            <div class="col-md-4 col-sm-3 col-xs-12"> 
                                <div class="form-group text-nowrap">
                                    <label for="startDate" class="text-left"  >{{ 'stats.export.configsheet.content_period_from'|trans({}, 'messages',  app.user.locale) }}</label>									
                                    <input type="date" v-model="filters.start" name="start" class="form-control" id="startDate"/>
                                    <small id="error-message" class="d-none form-text text-danger">
                                        {{ 'stats.export.error_start_date'|trans({}, 'messages',  app.user.locale) }}
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-3 col-xs-12">
                                <div class="form-group text-nowrap">
                                    <label for="endDate" class="text-left">{{ 'stats.export.configsheet.content_period_to'|trans({}, 'messages',  app.user.locale) }}</label>
                                    <input type="date" v-model="filters.end" name="end" class="form-control" id="endDate"/>
                                </div>
                            </div>
                        </div>
				</div>
				 <div class="modal-footer justify-content-center">
                        <button type="button" id="sendForm" class="btn btn-primary mr-1" @click="sendFormUser()" data-dismiss="modal">
                            <i class="fas fa-file-excel"></i> {{ 'stats.export_title'|trans({}, 'messages',  app.user.locale) }}
                        </button>
                        <button type="button" id="resetForm" class="btn btn-secondary ml-1" @click="initFilters">
                            <i class="fas fa-trash-restore"></i> {{ 'stats.export.reset_form'|trans({}, 'messages',  app.user.locale) }}
                        </button>
                    </div>
			</div>
		</div>
	</div>
