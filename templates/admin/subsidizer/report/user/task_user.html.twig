<div class="course-contents">
    <h3>{{'taskCourse.labelInPlural'|trans({}, 'messages',  app.user.locale) }}</h3>
    <table class="table datagrid with-rounded-top" width="100%">
        <thead class="thead-light">
        <tr>
            <th>{{'taskCourse.labelInSingular'|trans({}, 'messages',  app.user.locale) }}</th>
            <th><span>{% trans %}Date{% endtrans %}</span></th>
            <th><span>{{'state'|trans({}, 'messages',  app.user.locale) }}</span></th>
        </tr>
        </thead>

        <tbody>
        {% for task in taskUser[announcementUser.user.id] %}
            <tr>
                <td>
                    {{ task.title |raw }}
                </td>
                <td>
                    {{ task.updatedAt | date('Y-m-d H:i') }}
                </td>
                <td>
                    {% set state = 'taskCourse.configureFields.state_'~ task.state  %}
                    {{state|trans({}, 'messages',  app.user.locale) }}
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>