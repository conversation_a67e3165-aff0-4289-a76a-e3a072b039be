{% extends '@!EasyAdmin/crud/detail.html.twig' %}

{% block content_title %}
    {{filterCategory}} 
{% endblock content_title %}


{% block main %}
    <div>      
         <div>
            <a class=" action-new btn btn-primary mb-1" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\FilterCrudController').setAction('new').set('filterCategoryId', filterCategory.id).set('referrer', referrer) }}">{{ 'filter.configureFields.action_add'|trans({}, 'messages',  app.user.locale) }}</a>
        <br><br>
        </div>
    <div>
        <table class="table">
        <thead>
            <tr>            
            <th scope="col">{{ 'filter.label_in_plural'|trans({}, 'messages',  app.user.locale) }}</th>
            <th scope="col" class="text-right">{{ 'common_areas.actions'|trans({}, 'messages',  app.user.locale) }}</th>            
            </tr>
        </thead>
        <tbody>
            {% for fil in filters %}
            <tr>            
            <td>{{fil.name}} </td>
            <td class="text-right">            
            <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\FilterCrudController').setAction('edit').setEntityId(fil.id).set('referrer', referrer) }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
             <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\FilterCrudController').setAction('delete').setEntityId(fil.id).set('referrer', referrer) }}" class="btn btn-danger action-delete" formaction="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\FilterCrudController').setAction('delete').setEntityId(fil.id).set('referrer', referrer) }}" data-bs-toggle="modal" data-bs-target="#modal-delete"><i class="fa fa-trash"></i></a>
               
            </td>
         
            </tr>
            {% endfor %}
          
        </tbody>
        </table>
    </div>
    </div>

        {% block delete_form %}
        {{ include('@EasyAdmin/crud/includes/_delete_form.html.twig', with_context = false) }}
    {% endblock delete_form %}

{% endblock main %}


{% block body_javascript %}

    {{ parent() }}

    <script type="text/javascript">

        $(function() {
            $('.action-delete').on('click', function(e) {
                e.preventDefault();

                 const url = $(this).attr('href');               

                $('#modal-delete').modal({ backdrop: true, keyboard: true })
                    .off('click', '#modal-delete-button')
                    .on('click', '#modal-delete-button', function () {
                        let deleteForm = $('#delete-form');
                        deleteForm.attr('action', url);
                        deleteForm.trigger('submit');
                    });
            });
        });

    </script>
{% endblock %}