{% extends '@!EasyAdmin/crud/detail.html.twig' %}

{% block head_stylesheets %}
  {{ parent() }}
  {{ encore_entry_link_tags('task-course') }}

{% endblock %}

{% block content_title %}
  {% if task.announcement %}
    <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\AnnouncementCrudController').setAction('detail').setEntityId(task.announcement.id).set('tab','task') }}" class="card-link">
      {{ 'announcements.label_in_singular'|trans({}, 'messages') }}  : {{ task.course.name }}
    </a>
    {% else %}
      <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\CourseCrudController').setAction('detail').setEntityId(task.course.id) }}" class="card-link">{{ task.course.name }}</a>
  {% endif %}
{% endblock %}

{% block main %}
  <div class="card" style="width: 100%;">
    <div class="card-body">
      <h5 class="card-title">{{ task.title }}</h5>
      <h6 class="card-subtitle mb-2 text-muted"></h6>
      <p class="card-text">{{ task.description|raw }}</p>
    </div>
  </div>

  <div class="content-panel pb-3">
    <div class="page-actions pt-3 pr-3 text-right">
      <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#fileTask">{{ 'taskCourse.configureFields.addFile'|trans({}) }}</button>
    </div>

  </div>

  <div id="taskCourse">
    {% include 'admin/course/templates/task/add_file.html.twig' %}

    <div>
      <list-files-task />
    </div>

    <div>
      <history-task />
    </div>
  </div>

  <div  id="taskCourse">
  </div>

  	{% block delete_form %}
		{{ include('@EasyAdmin/crud/includes/_delete_form.html.twig', with_context = false) }}
	{% endblock delete_form %}
{% endblock %}

{% block body_javascript %}
  <script>
    let idCourse 			   = {{ task.course.id | json_encode | raw }};
    let idTask 				   = {{ task.id | json_encode | raw }};
  </script>
  {{ parent() }}
  {{ encore_entry_script_tags('task-course') }}
{% endblock %}
