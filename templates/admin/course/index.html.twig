{% extends '@!EasyAdmin/crud/index.html.twig' %}


{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('courseExport') }}
{% endblock %}

{% block content_title %}
    <div class="d-flex justify-content-between flex-wrap align-items-center">
        <div>
            {{ parent() }}
        </div>
        <div>
            <button data-toggle="modal" data-target="#modal-export" class="btn btn-primary">{{ 'course.export'|trans({}, 'messages', app.user.locale) }}</button>

{#            {% if entity != 'Nps' %}#}
{#                <a href="{{ ea_url().unsetAll().setController(controller).setAction('new') }}" type="button" class="btn btn-primary">#}
{#                    {{ 'common_areas.create'|trans({}, 'messages', app.user.locale) }}#}
{#                    {{ translateEntity |trans({}, 'messages', app.user.locale) }}#}
{#                </a>#}
{#            {% endif %}#}
            <a class=" action-new btn btn-primary"
            style="text-transform: capitalize;"
               href="{{ ea_url().setController('App\\Controller\\Admin\\CourseCrudController').setAction('createCourseView') }}">
                {{ 'common_areas.create'|trans({}, 'messages', app.user.locale) }}
                {{ translateEntity |trans({}, 'messages', app.user.locale) }}
            </a> 
        </div>
    </div>
{% endblock content_title %}

{% block main %}
    <div id="app" locale="{{ app.user.locale  }}" shared-url="{{ share }}">
        {{ parent() }}

        {% block import_clone_modal %}
            {{ include('admin/course/_clone_modal.html.twig', with_context = true) }}
        {% endblock import_clone_modal %}

        <div id="modal-export" class="modal fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body">
                        <h4>{{ 'course.export.confirm'|trans({}, 'messages', app.user.locale) }}</h4>
                        <div class="alert alert-warning" style="display: none;"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-secondary">
                            <span class="btn-label">{{ 'common_areas.cancel'|trans({}, 'messages', app.user.locale) }}</span>
                        </button>

                        <button type="button" data-dismiss="modal" class="btn btn-primary" @click="export_catalog()">
                            <span class="btn-label">{{ 'stats.export_title'|trans({}, 'messages', app.user.locale) }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>
{% endblock main %}


{% block body_javascript %}
    {{ parent() }}
    {{ encore_entry_script_tags('courseExport') }}
{% endblock %}
