{% extends '@!EasyAdmin/crud/detail.html.twig' %}

{% block content_title %}
    {{ documentation.title }}
{% endblock content_title %}

{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('player') }}
{% endblock %}

{% block main %}
    <div class="card mb-3">
        <div class="row no-gutters">
            <div class="card-header">{{ documentation.title }}</div>
            <div class="card-body">
                <span>{{ documentation.description | raw }}</span>
            </div>
            <div class="card-footer">
                {% if documentation.url is not null and documentation.type == 'video' %}
                    <a href="#" type="button" data-title="{{ documentation.title }}" data-url="https://player.vimeo.com/video/{{ identifier }}?color=ffffff&title=0&byline=0&portrait=0&rel=0" class="btn btn-sm btn-info play"><i class="fa fa-video"></i> {{ documentation.type | upper }}</a>
                {% endif %}
                {% if documentation.file is not null and documentation.type == 'pdf' %}
                    <a href="#" data-title="{{ documentation.title }}" data-url="{{ asset(documentation_uploads) }}/{{ documentation.file }}" class="btn btn-sm btn-info play"><i class="fa fa-file-pdf"></i> {{ documentation.type | upper }}</a>
                {% endif %}
            </div>
        </div>
    </div>

    {% block chapter_player %}
        {{ include('admin/player.html.twig', with_context = true) }}
    {% endblock %}

{% endblock main %}


{% block body_javascript %}
    {{ parent() }}
    {{ encore_entry_script_tags('player') }}
    {{ encore_entry_script_tags('iframeVimeo') }}
{% endblock %}
