{% extends '@!EasyAdmin/crud/detail.html.twig' %}

{% block main %}

  <div class="card">
  <div class="card-header">
   <h5> {{ 'opinions.configureFields.valoration'|trans({}, 'messages',  app.user.locale) }} {{course}} </h5>
  </div>
  <div class="card-body">
    {% for detail in detail %}
    {% if detail.main == 1 and detail.type =='text' %}
          <h5 class="card-title">{{ 'opinions.label_in_singular'|trans({}, 'messages',  app.user.locale) }}</h5>
          <p class="card-text">  {{detail.value}} </p>
            {% if detail.toPost == 1 %}
          <p class="badge badge-success"><span class="badge badge-success">Published</span></p>
       {% endif %}
     {% endif %}
    {% endfor %}

      {% for detail in detail %}
    {% if detail.main == 1 and detail.type =='nps' and detail.question.source == 1 %}
          <h5 class="card-title">{{ 'opinions.configureFields.valoration'|trans({}, 'messages',  app.user.locale) }}</h5>    <h2 class="card-text">
          <i class="fas fa-star"></i> {{detail.value /2}} </h2>
     {% endif %}
    {% endfor %}
  </div>

</div>

{% endblock main %}
