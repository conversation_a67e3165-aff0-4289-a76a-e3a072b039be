<div class="Forum">
    {% for thread in threads %}
        <table>
            <thead>
            <tr>
                <th colspan="2">
                    {{ thread.name }}
                </th>
            </tr>
            </thead>
            <tbody>
            {% for message in thread.messages %}
                <tr>
                    <td colspan="2">
                        [{{ message['createdAt'] | date('Y-m-d H:i') }}] {{ message['name'] }}: {{ message['message'] }}
                    </td>
                </tr>
                {% if message.replies|length > 0 %}
                    {% for reply in message.replies %}
                        <tr>
                            <td></td>
                            <td>[{{ reply['createdAt'] | date('Y-m-d H:i') }}] {{ reply['name'] }}: {{ reply['message'] }}</td>
                        </tr>
                    {% endfor %}
                {% endif %}
            {% endfor %}
            </tbody>
        </table>
        <hr>
    {% endfor %}
</div>
