message_api.content.scorm_correctly: 'Scorm correctamente iniciado'
message_api.content.token_not_send: 'Token no enviado'
message_api.content.error_get_content: 'Se ha producido un error al intentar iniciar el capítulo:'
message_api.content.scorm_user_not_access: 'El usuario no tiene acceso a este curso'
message_api.content.scorm_not_content: 'El capítulo no es un paquete de contenidos'
message_api.base.credentials_no_correct: 'Las credenciales introducidas no son correctas.'
message_api.base.authorized_access: 'Los datos de acceso no son validos'
message_api.base.user_successfully: 'El usuario ha cerrado la sesión con éxito'
message_api.base.error_login: 'Se ha producido un error al intentar iniciar la sesión del usuario - Error:'
message_api.base.authorized_token_fail: 'No se ha podido acceder a través de SSO. Si aún no se ha registrado y activado su usuario en la plataforma, deberá hacerlo para poder habilitar este tipo de acceso SSO.'
message_api.base.user_not_exists_in_ad: 'Credenciales incorrectas o usuario no existe en Directorio Activo'
message_api.controller.error_get_course: 'Se ha producido un error al intentar registrar el usuario - Error:'
message_api.controller.chapter_init: 'Capítulo correctamente iniciado'
message_api.controller.error_chapter_init: 'Se ha producido un error al intentar iniciar el capítulo:'
message_api.controller.error_chapter_update: 'Capítulo correctamente actualizado'
message_api.controller.help_text: 'Ayuda Los textos se recuperan con éxito'
message_api.controller.course_not_found: 'No se han encontrado cursos'
message_api.controller.user_not_access: 'El usuario no tiene acceso a este curso'
message_api.controller.evaluation_course: 'Evaluación enviada'
message_api.controller.training_itinerary: 'Mi formación'
message_api.controller.continue_training: 'Continuar viendo'
message_api.message.not_found: 'Mensaje no encontrado'
message_api.message.not_access: 'Acceso no permitido'
message_api.scorm.init: 'Scorm correctamente iniciado'
message_api.scorm.not_package: 'El capítulo no es un paquete SCORM'
message_api.scorm.button_add_scorm: 'Agregar paquete scorm'
message_api.scorm.button_delete_scorm: 'Eliminar paquete scorm'
message_api.scorm.title_package: 'Paquete scorm'
message_api.scorm.upload_file_scorm: 'Seleccionar archivo .zip'
message_api.scorm.info_upload_scorm: 'Debes cargar el paquete scorm'
message_api.notification.not_found: 'Notificación no encontrada'
message_api.player.no_content: 'Este capítulo no tiene contenido'
message_api.alert.minimal_question: 'Este juego requiere un mínimo de %number% preguntas'
message_api.alert.image_puzzle: 'El puzzle necesita una imagen para crear el juego.'
message_api.alert.question_correct: 'A esta pregunta debes agregarle una respuesta correcta'
message_api.alert.chapter_content: 'A este capítulo le hace falta contenido'
message_api.alert.pdf: 'A este capítulo le hace falta un pdf'
message_api.alert.video: 'A este capítulo le hace falta contenido'
message_api.alert.slider: 'Este capítulo tiene un mínimo de %number% imágenes'
message_api.alert.scorm: 'A este capítulo le hace falta paquete scorm'
message_api.alert.question_hidden: 'Esta pregunta sólo debe contener una respuesta y tiene que ser correcta'
message_api.alert.minimal_pair: 'Este capítulo tiene un mínimo de %number% parejas'
message_api.announcement.call_for_aplications: Convocatoria
message_api.alert.course_content_incomplete: 'Falta información en los capitulos, por favor revise y agregue contenido para poder continuar con la creación de la convocatoria.'
message_api.diploma.supered: 'Por haber superado'
message_api.diploma.granted: 'Concedido a'
message_api.diploma.date: Fecha
message_api.diploma.diploma: Diploma
message_api.diploma.trato: D./Dña
message_api.diploma.nif: 'con NIF'
message_api.diploma.services: 'que presta sus servicios en la Empresa'
message_api.diploma.cif: 'con CIF'
message_api.diploma.evaluation: 'Ha superado con evaluación positiva la Acción Formativa'
message_api.diploma.code: 'Código AF/GRUPO'
message_api.diploma.between: 'Durante los días'
message_api.diploma.duration: 'con una duración total de'
message_api.diploma.fomartionType: 'en la modalidad formativa'
message_api.diploma.courseContent: 'Contenidos del curso en la siguiente página'
message_api.diploma.signatureSeal: 'Firma y sello de la entidad responsable <br> de impartir la formación'
message_api.diploma.signatureDate: 'Fecha de expedición'
message_api.diploma.acreditation: Acreditativo
message_api.diploma.signature: 'Firma del participante'
message.api.diploma.type_course_default: Teleformación
message.api.diploma.dates: 'Fecha desde %dateFrom% hasta %dateTo%'
message.api.diploma.hours: '{0} %count% horas|{1} %count% hora|]1,Inf] %count% horas'
message.api.diploma.duration: Duración
message.api.diploma.with_dni: 'con %nameIdentification%  %dni%'
message_api.announcement.cancelation_announcement: 'Cancelación de la convocatoria %course%'
message_api.help_category.delete_error: 'No se puede eliminar la categoría %categoryName%, tiene contenido vinculado'
