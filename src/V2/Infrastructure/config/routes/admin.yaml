get_admin_users:
  path: /users
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetUsersController

get_admin_creators:
  path: users/creators
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetCreatorsController

get_admin_course_creators:
  path: /courses/{courseId}/creators
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetCourseCreatorsController

put_admin_course_creators:
  path: /courses/{courseId}/creators/{userId}
  methods: PUT
  controller: App\V2\Infrastructure\Controller\Admin\PutCourseCreatorController

delete_admin_course_creator:
  path: courses/{courseId}/creators/{userId}
  methods: DELETE
  controller: App\V2\Infrastructure\Controller\Admin\DeleteCourseCreatorController

post_lti_registration:
  path: /lti/registrations
  methods: POST
  controller: App\V2\Infrastructure\Controller\Admin\PostLtiRegistrationController
