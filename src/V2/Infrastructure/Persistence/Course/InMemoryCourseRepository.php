<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Course;

use App\Entity\Course;
use App\V2\Domain\Course\CourseCollection;
use App\V2\Domain\Course\CourseCriteria;
use App\V2\Domain\Course\CourseRepository;
use App\V2\Domain\Course\Exceptions\CourseNotFoundException;
use App\V2\Domain\Shared\Collection\CollectionException;

class InMemoryCourseRepository implements CourseRepository
{
    private array $courses = [];

    #[\Override]
    public function put(Course $course): void
    {
        $this->courses[$course->getId()] = $course;
    }

    public function findOneBy(CourseCriteria $criteria): Course
    {
        $courses = $this->filterByCriteria($criteria);

        if (empty($courses)) {
            throw new CourseNotFoundException();
        }

        return reset($courses);
    }

    /**
     * @throws CollectionException
     */
    public function findBy(CourseCriteria $criteria): CourseCollection
    {
        return new CourseCollection(
            array_values($this->filterByCriteria($criteria))
        );
    }

    public function countBy(CourseCriteria $criteria): int
    {
        return \count($this->filterByCriteria($criteria));
    }

    private function filterByCriteria(CourseCriteria $criteria): array
    {
        $courses = array_filter(
            $this->courses,
            fn (Course $course) => (null === $criteria->getId() || $course->getId() === $criteria->getId()->value())
                && (null === $criteria->getIds() || \in_array($course->getId(), array_values($criteria->getIds()->all())))
        );

        return array_map(
            fn (Course $course) => (new Course())
                ->setId($course->getId())
                ->setCode($course->getCode())
                ->setName($course->getName())
                ->setDescription($course->getDescription())
                ->setCategories($course->getCategories())
                ->setOpen($course->getOpen())
                ->setLocale($course->getLocale())
                ->setTranslation($course->getTranslation())
                ->setPoints($course->getPoints())
                ->setCategory($course->getCategory())
                ->setGeneralInformation($course->getGeneralInformation())
                ->setActive($course->getActive())
                ->setManagers($course->getManagers())
                ->setDocumentation($course->getDocumentation())
                ->setLevel($course->getLevel())
                ->setTags($course->getTags())
                ->setOpenVisible($course->getOpenVisible())
                ->setFilters($course->getFilters())
                ->setIsNew($course->getIsNew())
                ->setTypeCourse($course->getTypeCourse())
                ->setNewAt($course->getNewAt())
                ->setIsNew($course->getIsNew())
                ->setTypeDiploma($course->getTypeDiploma())
                ->setIsContentDiploma($course->isIsContentDiploma())
                ->setTypeIndexDiploma($course->getTypeIndexDiploma())
                ->setDescriptionContentDiploma($course->getDescriptionContentDiploma())
                ->setDuration($course->getDuration())
                ->setCreatedAt($course->getCreatedAt())
                ->setUpdatedAt($course->getUpdatedAt())
                ->setDeletedAt($course->getDeletedAt())
                ->setCreatedBy($course->getCreatedBy())
                ->setUpdatedBy($course->getUpdatedBy())
                ->setDeletedBy($course->getDeletedBy())
                ->setThumbnails($course->getThumbnails()),
            $courses
        );
    }
}
