<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Application\Hydrator\LTI\LtiRegistrationHydratorCollection;
use App\V2\Application\Log\Logger;
use App\V2\Domain\LTI\Exceptions\LtiPlatformNotFoundException;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\Exceptions\LtiToolNotFoundException;
use App\V2\Domain\LTI\LtiDeployment;
use App\V2\Domain\LTI\LtiPlatformCriteria;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationHydrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\LTI\LtiToolCriteria;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\LTI\Transformer\LtiPlatformTransformer;
use App\V2\Infrastructure\LTI\Transformer\LtiToolTransformer;
use OAT\Library\Lti1p3Core\Registration\RegistrationFactory;
use OAT\Library\Lti1p3Core\Registration\RegistrationInterface;
use OAT\Library\Lti1p3Core\Registration\RegistrationRepositoryInterface;

readonly class RegistrationRepository implements RegistrationRepositoryInterface
{
    public function __construct(
        private RegistrationFactory $registrationFactory,
        private LtiRegistrationRepository $ltiRegistrationRepository,
        private LtiPlatformRepository $ltiPlatformRepository,
        private LtiToolRepository $ltiToolRepository,
        private LtiRegistrationHydratorCollection $hydratorCollection,
        private CoreKeyChainGenerator $coreKeyChainGenerator,
        private Logger $logger,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidUuidException
     * @throws HydratorException
     */
    public function find(string $identifier): ?RegistrationInterface
    {
        try {
            $ltiRegistration = $this->ltiRegistrationRepository->findOneBy(
                LtiRegistrationCriteria::createEmpty()
                    ->filterById(new Uuid($identifier))
            );
        } catch (LtiRegistrationNotFoundException) {
            $this->logger->error('Registration with identifier "' . $identifier . '" not found');

            return null;
        }

        $this->hydrate(new LtiRegistrationCollection([$ltiRegistration]));

        return $this->fromLtiRegistrationToCoreRegistration($ltiRegistration);
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     */
    public function findAll(): array
    {
        $registrations = $this->ltiRegistrationRepository->findBy(
            LtiRegistrationCriteria::createEmpty()
        );

        $this->hydrate($registrations);

        $result = [];
        foreach ($registrations as $registration) {
            $result[] = $this->fromLtiRegistrationToCoreRegistration($registration);
        }

        return $result;
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     */
    public function findByClientId(string $clientId): ?RegistrationInterface
    {
        try {
            $ltiRegistration = $this->ltiRegistrationRepository->findOneBy(
                LtiRegistrationCriteria::createEmpty()->filterByClientId($clientId)
            );
        } catch (LtiRegistrationNotFoundException) {
            $this->logger->error('Registration with client id "' . $clientId . '" not found');

            return null;
        }

        $this->hydrate(new LtiRegistrationCollection([$ltiRegistration]));

        return $this->fromLtiRegistrationToCoreRegistration($ltiRegistration);
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     */
    public function findByPlatformIssuer(string $issuer, ?string $clientId = null): ?RegistrationInterface
    {
        try {
            $ltiPlatform = $this->ltiPlatformRepository->findOneBy(
                LtiPlatformCriteria::createEmpty()->filterByAudience($issuer)
            );
        } catch (LtiPlatformNotFoundException) {
            return null;
        }

        if (null === $clientId) {
            return $this->find($ltiPlatform->getRegistrationId()->value());
        }

        try {
            $ltiRegistration = $this->ltiRegistrationRepository->findOneBy(
                LtiRegistrationCriteria::createEmpty()
                    ->filterById($ltiPlatform->getRegistrationId())
                    ->filterByClientId($clientId)
            );

            $this->hydrate(new LtiRegistrationCollection([$ltiRegistration]));

            return $this->fromLtiRegistrationToCoreRegistration($ltiRegistration);
        } catch (LtiRegistrationNotFoundException) {
            return null;
        }
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     */
    public function findByToolIssuer(string $issuer, ?string $clientId = null): ?RegistrationInterface
    {
        try {
            $ltiTool = $this->ltiToolRepository->findOneBy(
                LtiToolCriteria::createEmpty()->filterByAudience($issuer)
            );
        } catch (LtiToolNotFoundException) {
            return null;
        }

        if (null === $clientId) {
            return $this->find($ltiTool->getRegistrationId()->value());
        }

        try {
            $ltiRegistration = $this->ltiRegistrationRepository->findOneBy(
                LtiRegistrationCriteria::createEmpty()
                    ->filterById($ltiTool->getRegistrationId())
                    ->filterByClientId($clientId)
            );

            $this->hydrate(new LtiRegistrationCollection([$ltiRegistration]));

            return $this->fromLtiRegistrationToCoreRegistration($ltiRegistration);
        } catch (LtiRegistrationNotFoundException) {
            return null;
        }
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     */
    private function hydrate(LtiRegistrationCollection $collection): void
    {
        $this->hydratorCollection->hydrate(
            $collection,
            LtiRegistrationHydrationCriteria::createEmpty()
                ->withDeployments()
                ->withPlatform()
                ->withTool()
        );
    }

    private function fromLtiRegistrationToCoreRegistration(
        LtiRegistration $registration,
    ): RegistrationInterface {
        return $this->registrationFactory->create(
            identifier: $registration->getId()->value(),
            clientId: $registration->getClientId(),
            platform: LtiPlatformTransformer::fromLtiPlatformToCorePlatform($registration->getPlatform()),
            tool: LtiToolTransformer::fromLtiToolToCoreTool($registration->getTool()),
            deploymentIds: array_reduce(
                $registration->getDeployments()->all(),
                function (array $carry, LtiDeployment $deployment) {
                    $carry[] = $deployment->getDeploymentId();

                    return $carry;
                },
                []
            ),
            platformKeyChain: $this->coreKeyChainGenerator->generateCoreKeyChain(), // When EL is working as platform
            toolKeyChain: $this->coreKeyChainGenerator->generateCoreKeyChain(), // When EL is working as tool
            platformJwksUrl: $registration->getPlatform()->getJwksUrl()->value(),
            toolJwksUrl: $registration->getTool()->getJwksUrl()->value(),
        );
    }
}
