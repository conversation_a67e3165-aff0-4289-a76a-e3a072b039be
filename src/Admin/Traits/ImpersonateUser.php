<?php

namespace App\Admin\Traits;

use App\Entity\AnnouncementInspectorAccess;
use App\Entity\User;
use App\Entity\UserToken;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

trait ImpersonateUser
{
    public function impersonateAsInspector(
        EntityManagerInterface $em,
        int $userId,
        array $extra = []
    )
    {
        $userToImpersonate = $em->getRepository(User::class)->find($userId);
        if (!$userToImpersonate) return FALSE;

        $extra['time'] = (new \DateTime())->getTimestamp();

        $token = hash('sha256', json_encode($extra));

        $userToken = new UserToken();
        $userToken->setToken($token)
            ->setExtra($extra)
            ->setValidUntil((new \DateTimeImmutable())->modify('+1 minute'))
            ->setUser($userToImpersonate)
            ->setType(UserToken::TYPE_INSPECTOR_IMPERSONATE_USER);

        $em->persist($userToken);
        $em->flush();

        return "/campus/?token=$token";
    }
}
