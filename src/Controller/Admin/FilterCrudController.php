<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Field\TranslationField;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class FilterCrudController extends AbstractCrudController
{
    use SerializerTrait;
    private EntityManagerInterface $em;
    private RequestStack $requestStack;
    private LoggerInterface $logger;
    protected TranslatorInterface $translator;
    private AdminContextProvider $context;
    private SettingsService $settingsService;

    public function __construct(EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger, AdminContextProvider $context, TranslatorInterface $translator, SettingsService $settingsService)
    {
        $this->em = $em;
        $this->requestStack = $requestStack;
        $this->logger = $logger;
        $this->context = $context;
        $this->translator = $translator;
        $this->settingsService = $settingsService;
    }

    public static function getEntityFqcn(): string
    {
        return Filter::class;
    }

    public function configureFields(string $pageName): iterable
    {
        $entityId = $this->requestStack->getCurrentRequest()->get('entityId');

        yield IdField::new('id', '#')->hideOnForm();
        if (Crud::PAGE_EDIT === $pageName && $this->getParameter('app.multilingual')) {
            yield FormField::addPanel($this->translator->trans('filter_category.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
                ->addCssClass('col-xs-12 col-md-6');
        }

        yield TextField::new('name', $this->translator->trans('filter.configureFields.name', [], 'messages', $this->getUser()->getLocale()))
            ->setColumns('col-xs-12 col-md-6');

        if (Crud::PAGE_EDIT === $pageName) {
            /**
             * Parent.
             */
            $filterRepository = $this->em->getRepository(Filter::class);
            $parents = $filterRepository->getParents($entityId ? [$entityId] : []);
            yield AssociationField::new('parent', 'Category parent')
                ->setFormTypeOptions([
                    'choices' => $parents,
                ])->setColumns('col-xs-12 col-md-6');
        }

        if (Crud::PAGE_EDIT === $pageName && $this->getParameter('app.multilingual')) {
            /*
             * Translations
             */
            yield FormField::addPanel($this->translator->trans('course_category.configureFields.translations', [], 'messages', $this->getUser()->getLocale()))
                ->addCssClass('translation-form-panel form-panel-hide-legend col-xs-12 col-md-6');
            yield TranslationField::new('translations', $this->translator->trans('help_category.configureFields.translations', [], 'messages', $this->getUser()->getLocale()), [
                'name' => [
                    'label' => $this->translator->trans('filter.configureFields.name', [], 'messages', $this->getUser()->getLocale()),
                ],
            ]);
        }
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular($this->translator->trans('filter.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
            ->setEntityLabelInPlural($this->translator->trans('filter.label_in_plural', [], 'messages', $this->getUser()->getLocale()))
            ->addFormTheme('@FOSCKEditor/Form/ckeditor_widget.html.twig')
            ->setSearchFields(['id', 'title', 'description', 'image'])
            ->addFormTheme('@A2lixTranslationForm/bootstrap_4_layout.html.twig')
            ->overrideTemplate('crud/detail', 'admin/filter/detail.html.twig');
        /*  ->overrideTemplate('crud/edit', 'admin/chapter/edit.html.twig') */
        /* ->overrideTemplate('crud/new', 'admin/filter/new.html.twig'); */
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if ($this->requestStack->getCurrentRequest()->get('filterCategoryId')) {
            $responseParameters->set('filterCategory', $this->em->getRepository(FilterCategory::class)->find($this->requestStack->getCurrentRequest()->get('filterCategoryId')));
        }

        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName')) {
            $filterRepository = $this->em->getRepository(Filter::class);
            $entity = $this->context->getContext()->getEntity();
            $filters = $filterRepository->find($entity->getPrimaryKeyValue());

            $filterCategoryRepository = $this->em->getRepository(FilterCategory::class);
            $filtersCategory = $filterCategoryRepository->find([
                'id' => $filters->getId(),
            ]);

            $filtersAll = $filterRepository->findBy([
                'filterCategory' => $filtersCategory,
            ]);

            $adminUrlGenerator = $this->get(AdminUrlGenerator::class);
            $referrer = $adminUrlGenerator
                ->unsetAll()
                ->setController(FilterCrudController::class)
                ->setAction('detail')
                ->setEntityId($filtersCategory->getId())
                ->generateUrl();

            $responseParameters->set('filterCategory', $filtersCategory);
            $responseParameters->set('filters', $filtersAll);
            $responseParameters->set('referrer', $referrer);
        }

        return $responseParameters;
    }

    public function createEntity(string $entityFqc)
    {
        $filter = new Filter();
        $filter->setSource(Filter::SOURCE_LOCAL);

        $filterCategoryRepository = $this->em->getRepository(FilterCategory::class);
        $filterCategory = $filterCategoryRepository->find($this->requestStack->getCurrentRequest()->get('filterCategoryId'));

        $filter->setFilterCategory($filterCategory);

        return $filter;
    }

    /**
     * @Route ("/admin/filter-categories", name="admin_filter_categories")
     *
     * @return Response
     */
    public function getFilterCategories()
    {
        // $filter_categories = $this->em->getRepository(FilterCategory::class)->findAll();
        $filter_categories = $this->em->getRepository(FilterCategory::class)
            ->getListFilterCategoryTranslate($this->getUser()->getLocale());
        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => ['filterCategories' => $filter_categories],
        ];

        return $this->sendResponse($response, ['groups' => 'user_area']);
    }

    /**
     * @Rest\Get("/admin/filter/categories")
     */
    public function loadFilterCategories(): Response
    {
        $user = $this->getUser();

        $results = $this->em->getRepository(FilterCategory::class)
            ->findFiltersAndCategoriesForUser($user);

        $categories = [];

        foreach ($results as $row) {
            $categoryId = $row['categoryId'];

            if (!isset($categories[$categoryId])) {
                $categories[$categoryId] = [
                    'id' => $categoryId,
                    'name' => $row['categoryName'],
                ];
            }
        }

        $categories = array_values($categories);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $categories,
        ]);
    }

    /**
     * @Rest\Get("/admin/filter/list-with-category")
     */
    public function getFiltersAndCategories(): Response
    {
        $user = $this->getUser();
        $results = $this->em->getRepository(FilterCategory::class)
            ->findFiltersAndCategoriesForUser($user);

        $data = [];
        foreach ($results as $result) {
            $categoryId = $result['categoryId'];
            if (!isset($data[$categoryId])) {
                $data[$categoryId] = [];
            }

            $data[$categoryId][] = [
                'id' => $result['filterId'],
                'name' => $result['filterName'],
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route ("/admin/filters/categories/{id}", name="admin_filters")
     *
     * @return Response
     */
    public function getFilters(FilterCategory $filterCategory)
    {
        $locale = $this->getUser()->getLocale() ?? $this->settingsService->get('app.adminDefaultLanguage');
        $filters = $this->em->getRepository(Filter::class)
            ->getFiltersTranslatedByFilterCategory($filterCategory, $locale);

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => ['filters' => $filters],
        ];

        return $this->sendResponse($response, ['groups' => 'user_area']);
    }

    /**
     * @Route ("/admin/filters/prevencion/managers", name="prevencion_filters")
     *
     * @return Response
     */
    public function assignFiltersToManagersByFilter()
    {
        // IMQ PREVENCION = 250;

        $filters_ref = [250];
        $filters_ref = [309, 311, 313, 315, 316, 317, 318, 319, 323, 324, 325, 329, 330, 331, 332, 342, 348, 351, 353, 354, 356, 358, 359, 360];

        $managers_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        $filters = [];

        foreach ($filters_ref as $filter_id) {
            $filter_ref = $this->em->getRepository(Filter::class)->find($filter_id);

            /**
             * @var $users User[]
             */
            $users = $this->em->getRepository(User::class)->getUsersByFilter($filter_ref);

            foreach ($users as $user) {
                foreach ($user->getFilter() as $filter) {
                    if (!\in_array($filter, $filters)) {
                        $filters[$filter->getId()] = $filter;
                    }
                }
            }
        }

        if (empty($managers_emails)) {
            /**
             * @var $managers User[]
             */
            $managers = $this->em->getRepository(User::class)->getManagersByFilter($filters_ref[0]);
        } else {
            $managers = [];
            foreach ($managers_emails as $email) {
                $manager = $this->em->getRepository(User::class)->findOneBy(['email' => $email]);
                if ($manager) {
                    array_push($managers, $manager);
                }
            }
        }

        foreach ($managers as $manager) {
            foreach ($filters as $filter) {
                if (\in_array($filter->getFilterCategory()->getId(), [2, 4])) {
                    $manager->addManagerFilter($filter);
                }
            }
        }

        $this->em->flush();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => ['filters' => $managers],
        ];

        return $this->sendResponse($response, ['groups' => 'user_area']);
    }
}
