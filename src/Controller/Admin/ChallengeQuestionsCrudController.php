<?php

namespace App\Controller\Admin;

use App\Admin\Field\TranslationField;
use App\Admin\Field\VichImageField;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Challenge;
use App\Entity\ChallengeAnswers;
use App\Entity\ChallengeAnswersTranslation;
use App\Entity\ChallengeQuestions;
use App\Entity\ChallengeQuestionsTranslation;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ImageField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\LocaleField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\CrudUrlGenerator;
use KMS\FroalaEditorBundle\Form\Type\FroalaEditorType;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;


class ChallengeQuestionsCrudController extends AbstractCrudController
{
    use SerializerTrait;

    private $logger;
    private $requestStack;
    private $em;
    private $context;
    private $translator;
    private $settings;

    public function __construct(LoggerInterface $logger, RequestStack $requestStack, EntityManagerInterface $em, AdminContextProvider $context, TranslatorInterface $translator, SettingsService $settings)
    {

        $this->logger       = $logger;
        $this->requestStack = $requestStack;
        $this->em           = $em;
        $this->context      = $context;
        $this->translator = $translator;
        $this->settings = $settings;
    }

    public static function getEntityFqcn(): string
    {
        return ChallengeQuestions::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->overrideTemplate('crud/detail', 'admin/challenge/questions/detail.html.twig')
            ->addFormTheme('@KMSFroalaEditor/Form/froala_widget.html.twig')
            ->addFormTheme('@A2lixTranslationForm/bootstrap_4_layout.html.twig');
    }

    public function configureFields(string $pageName): iterable
    {
        $panel1 = FormField::addPanel('Basic information');
        $title = TextField::new('text');
        $challenge =  AssociationField::new('desafio');
        $imageFile = VichImageField::new('imageFile');
        $image = ImageField::new('image')->setBasePath($this->settings->get('app.challenge_question_uploads_path'));

        $random = BooleanField::new('random');

        $translations = TranslationField::new('translations', $this->translator->trans('help_text_content.configureFields.translations', [], 'messages', $this->getUser()->getLocale()), [
            'text' => [
                'field_type' => FroalaEditorType::class,
                'label' => $this->translator->trans('help_text_content.configureFields.text', [], 'messages', $this->getUser()->getLocale())
            ]
        ]);

        $translationsPanel = FormField::addPanel($this->translator->trans('course_category.configureFields.translations', [], 'messages', $this->getUser()->getLocale()))
            ->addCssClass('translation-form-panel form-panel-hide-legend');

        $id = IntegerField::new('id', 'ID');

        if ($this->settings->get('app.multilingual') == true) {
            $locale = LocaleField::new('locale');
        }

        if ($this->settings->get('app.setCoursePoints') == true) {
            $points = IntegerField::new('points');
        }

        if (Crud::PAGE_INDEX === $pageName) {
            $fields = [$panel1, $title, $challenge,  $random, $image];

            //            if(isset($locale)){
            //                $translation = TextField::new('translation');
            //                array_splice($fields, 4, 0, [$locale]);
            //                array_splice($fields, 9, 0, [$translation]);
            //            }

            return $fields;
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            return  [$title, $challenge,  $random];
        } elseif (Crud::PAGE_NEW === $pageName || Crud::PAGE_EDIT) {

            $pointsPosition = 4;

            $fields = [
                $panel1,
                $title->setColumns('col-xs-12 col-md-6'),
                $challenge->setColumns('col-xs-12 col-md-6'),
                $random->setColumns('col-xs-12 col-md-6'),
                $imageFile->setColumns('col-xs-12 col-md-6'),
                $translationsPanel,
                $translations
            ];

            //            if(isset($locale)) {
            //                if(Crud::PAGE_NEW === $pageName) $locale->setFormTypeOptionIfNotSet('data', $this->settings->get('app.defaultLanguage'));
            //                array_splice($fields, 2, 0, [$locale]);
            //                $pointsPosition++;
            //            }

            //  if(isset($points)) array_splice($fields, $pointsPosition, 0, [$points]);

            return $fields;
        }
    }

    public function configureActions(Actions $actions): Actions
    {
        $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
        //        if($this->settings->get('app.multilingual') == true){
        //            $translateAction = Action::new('translate', 'Translate')
        //                ->linkToCrudAction('translateAction')
        //                ->displayIf(static function ($entity) {
        //                    return is_null($entity->getTranslation());
        //                });
        //
        //            $actions
        //                ->add(Crud::PAGE_INDEX, $translateAction);
        //        }

        return $actions;
    }

    public function importTestQuestions(Request $request)
    {
        $challengeId = $request->get('challenge_id');
        $challengeRepository = $this->em->getRepository(Challenge::class);
        $challenge = $challengeRepository->find($challengeId);
        $challengeQuestions = $request->files->get('challenge_questions');

        $questionsArray = [];
        $answersArray = [];

        if ($this->checkDataUploadIntengrity($challengeQuestions)['error']) {
            $response = $this->checkDataUploadIntengrity($challengeQuestions);
        } else {

            $spreadsheet = $this->loadExcelFromPath($challengeQuestions);

            $primarySheet = $spreadsheet->setActiveSheetIndexByName($this->settings->get('app.defaultLanguage'))->toArray(null, true, true, true);

            $sheetSize = count($primarySheet);
            $this->logger->error("Sheetsize: " . $sheetSize);

            for ($i = 2; $i <= $sheetSize; $i++) {
                if ($primarySheet[$i]["A"] === "" || is_null($primarySheet[$i]["A"])) break;
                $question = new ChallengeQuestions();
                $question->setText($primarySheet[$i]["B"])
                    ->setRandom($primarySheet[$i]["A"])
                    ->setDesafio($challenge);
                $this->em->persist($question);

                $questionsArray[] =  $question;

                $letterArray = array('D', 'E', 'F', 'G', 'H', 'I');
                $position = 0;

                foreach ($letterArray as $letra) {
                    $position++;

                    if ($primarySheet[$i][$letra] != '') {
                        $challengeRespuesta = new ChallengeAnswers();
                        $challengeRespuesta->setPregunta($question)
                            ->setText($primarySheet[$i][$letra])
                            ->setCorrect((int)$primarySheet[$i]["C"] === $position);
                        $this->em->persist($challengeRespuesta);

                        $answersArray[] = $challengeRespuesta;
                    }
                }
            }

            if (!empty($questionsArray)) {
                foreach ($this->settings->get('app.languages') as $language) {
                    if ($language !== $this->settings->get('app.defaultLanguage')) {
                        $newTransSheet = $spreadsheet->setActiveSheetIndexByName($language)->toArray(null, true, true, true);;
                        $sheetSize = count($newTransSheet);

                        $questionPosition = 0;

                        for ($q = 2; $q <= $sheetSize; $q++) {
                            if ($newTransSheet[$q]["A"] === "" || is_null($newTransSheet[$q]["A"])) break;

                            $questionTranslatable = new ChallengeQuestionsTranslation();
                            $questionTranslatable->setText($newTransSheet[$q]["B"]);
                            $questionTranslatable->setLocale($language);
                            $questionTranslatable->setTranslatable($questionsArray[$questionPosition]);
                            $questionPosition++;
                            $this->em->persist($questionTranslatable);

                            $arrLetras = array('B', 'C', 'D', 'E', 'F', 'G');

                            $answerPosition = 0;
                            foreach ($arrLetras as $letra) {
                                if ($newTransSheet[$q][$letra] != '') {
                                    $challengeRespuesta = new ChallengeAnswersTranslation();
                                    $challengeRespuesta->setTranslatable($answersArray[$answerPosition]);
                                    $challengeRespuesta->setLocale($language);
                                    $challengeRespuesta->setText($newTransSheet[$q][$letra]);
                                    $this->em->persist($challengeRespuesta);
                                    $answerPosition++;
                                }
                            }
                        }
                    }
                }
            }

            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error'  => false,
                'data'   => [],
            ];
        }

        return $this->sendResponse($response, ['groups' => ['test_admin']]);
    }

    private function checkDataUploadIntengrity($file)
    {
        $message = '';
        $error = false;

        if (!$file->getPathname()) {
            $message = 'Error en la carga del fichero';
        }

        $spreadsheet = $this->loadExcelFromPath($file);

        $numberOfSheets = $spreadsheet->getSheetCount();

        $lastSheetCount = null;
        for ($i = 0; $i < $numberOfSheets - 1; $i++) {
            $activeSheet = $spreadsheet->setActiveSheetIndex($i)->toArray(null, true, true, true);
            $activeCount = 0;

            foreach ($activeSheet as $active) {
                if ($active["A"] !== "" && !is_null($active["A"])) {
                    $activeCount++;
                }
            }


            if ($lastSheetCount) {
                if ($lastSheetCount !== $activeCount) {
                    $message = "error en el numero de filas por idioma";
                    $i = 1000000;
                }
            }

            $lastSheetCount = $activeCount;
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error'  => $error,
            'data'   => $message,
        ];

        return $response;
    }

    private function loadExcelFromPath($challengeQuestions)
    {
        $reader = new Xlsx();
        $reader->setReadDataOnly(true);
        return $reader->load($challengeQuestions->getPathname());
    }

    public function downloadTemplate(Request $request)
    {
        $spreadsheet = new Spreadsheet();
        $sheet       = $spreadsheet->getActiveSheet();

        foreach ($this->settings->get('app.languages') as $language) {
            if ($language !== $this->settings->get('app.defaultLanguage')) {
                $newSheet = $spreadsheet->createSheet();
                $newSheet->setTitle($language);

                $headerRow = [
                    $this->translator->trans('challenges.question', [], 'messages', $language),
                    $this->translator->trans('challenges.answer1', [], 'messages', $language),
                    $this->translator->trans('challenges.answer2', [], 'messages', $language),
                    $this->translator->trans('challenges.answer3', [], 'messages', $language),
                    $this->translator->trans('challenges.answer4', [], 'messages', $language),
                    $this->translator->trans('challenges.answer5', [], 'messages', $language),
                    $this->translator->trans('challenges.answer6', [], 'messages', $language),
                ];

                $newSheet->fromArray($headerRow);
            } else {

                $headerRow = [
                    $this->translator->trans('challenges.random', [], 'messages', $language),
                    $this->translator->trans('challenges.question', [], 'messages', $language),
                    $this->translator->trans('challenges.correct', [], 'messages', $language),
                    $this->translator->trans('challenges.answer1', [], 'messages', $language),
                    $this->translator->trans('challenges.answer2', [], 'messages', $language),
                    $this->translator->trans('challenges.answer3', [], 'messages', $language),
                    $this->translator->trans('challenges.answer4', [], 'messages', $language),
                    $this->translator->trans('challenges.answer5', [], 'messages', $language),
                    $this->translator->trans('challenges.answer6', [], 'messages', $language),
                ];

                $sheet->setTitle($this->settings->get('app.defaultLanguage'));

                $sheet->fromArray($headerRow);
            }
        }

        $styleArrayFirstRow = ['font' => ['bold' => true,]];
        $highestColumn      = $sheet->getHighestColumn();
        $sheet->getStyle('A1:' . $highestColumn . '1')->applyFromArray($styleArrayFirstRow);
        foreach (range('A', $sheet->getHighestColumn()) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, "Xlsx");


        $fileName = "template.xlsx";
        $tempFile = tempnam(sys_get_temp_dir(), $fileName);
        $writer->save($tempFile);

        $date = date('Y-m-d_Hi');
        return $this->file($tempFile, $fileName, ResponseHeaderBag::DISPOSITION_INLINE);
    }

    /**
     * @Route ("/admin/challengeQuestions/challenge/{id}", name="challenge", requirements={"id":"\d+"})
     * @param Challenge $challenge
     * @return Response
     */
    public function getQuestions(Challenge $challenge)
    {
        $challengeQuestionRepository = $this->em->getRepository(ChallengeQuestions::class);


        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => [
                'questions' => $challengeQuestionRepository->findBy(['desafio' => $challenge]),
            ],
        ];

        return $this->sendResponse($response, ['groups' => ['admin_course']]);
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName')) {
            $challengeQuestionRepository  = $this->em->getRepository(ChallengeQuestions::class);
            $entity                       = $this->context->getContext()->getEntity();
            $question                     = $challengeQuestionRepository->find($entity->getPrimaryKeyValue());

            $challengeAnswersRepository = $this->em->getRepository(ChallengeAnswers::class);
            $answers = $challengeAnswersRepository->findBy(['pregunta' => $question]);

            $challengeRepository = $this->em->getRepository(Challenge::class);
            $challenge = $challengeRepository->find($question->getDesafio());

            $crudUrlGenerator = $this->get(CrudUrlGenerator::class);
            $referrer         = $crudUrlGenerator->build()
                ->unsetAll()
                ->setController(ChallengeQuestionsCrudController::class)
                ->setAction('detail')
                ->setEntityId($question->getId())
                ->generateUrl();

            $responseParameters->set('question', $question);
            $responseParameters->set('challenge', $challenge);
            $responseParameters->set('answers', $answers);
            $responseParameters->set('referrer', $referrer);
        }
        return $responseParameters;
    }
}
