<?php

namespace App\Controller\Admin;

use App\Admin\Field\FosCkeditorField;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Center;
use App\Entity\Department;
use App\Entity\EmailRecipient;
use App\Entity\EmailTemplate;
use App\Entity\ProfessionalCategory;
use App\Entity\User;
use App\Repository\EmailRecipientRepository;
use App\Repository\EmailTemplateRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\Field;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\ChoiceFilter;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use FOS\CKEditorBundle\Form\Type\CKEditorType;
use KMS\FroalaEditorBundle\Form\Type\FroalaEditorType;
use Knp\Component\Pager\PaginatorInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;


class EmailTemplateCrudController extends AbstractCrudController
{
    use SerializerTrait;

    private SettingsService $settings;
    private EntityManagerInterface $em;
    private AdminContextProvider $context;
    private LoggerInterface $logger;
    private JWTManager $jwt;
    protected TranslatorInterface  $translator;
    protected AdminUrlGenerator $adminUrlGenerator;
    protected MailerInterface $mailer;


    public function __construct (SettingsService $settings, EntityManagerInterface $em,
                                 AdminContextProvider $context, LoggerInterface $logger, JWTManager $jwt,
                                 TranslatorInterface $translator, AdminUrlGenerator $adminUrlGenerator,
                                 MailerInterface $mailer)
    {
        $this->settings            = $settings;
        $this->em                = $em;
        $this->context           = $context;
        $this->logger            = $logger;
        $this->jwt               = $jwt;
        $this->translator        = $translator;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->mailer            = $mailer;
    }


    public static function getEntityFqcn (): string
    {
        return EmailTemplate::class;
    }


    public function configureCrud (Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Email')
            ->setEntityLabelInPlural('Emails')
            ->addFormTheme('@FOSCKEditor/Form/ckeditor_widget.html.twig')
            ->setDefaultSort(['createdAt' => 'DESC'])
            ->setSearchFields(['subject'])
            ->overrideTemplate('crud/detail', 'admin/emailing/detail.html.twig');
    }


    public function configureActions (Actions $actions): Actions
    {
        $actions->update(Crud::PAGE_INDEX, Action::EDIT, function (Action $action) {
            return $action->displayIf(fn($entity) => !$entity->isSent());
        });

        $sendAction = Action::new('send', $this->translator->trans('emailing.send', [], 'messages', $this->getUser()->getLocale()))
            ->linkToCrudAction('sendEmail')
            ->displayIf(fn($entity) => !$entity->isSent());

        $actions->add(Crud::PAGE_INDEX, $sendAction)
            ->add(Crud::PAGE_INDEX, Action::DETAIL);

        return parent::configureActions($actions);
    }


    public function configureFields (string $pageName): iterable
    {
        $subject   = TextField::new('subject', $this->translator->trans('emailing.configureFields.subject', [], 'messages', $this->getUser()->getLocale()));
        $body = FosCkeditorField::new('body',$this->translator->trans('emailing.configureFields.body', [], 'messages', $this->getUser()->getLocale()))
            ->setFormTypeOptions(['config_name' => 'basic'])
            ->setHelp($this->translator->trans('emailing.configureFields.body_tags', [], 'messages', $this->getUser()->getLocale()) . ': [name], [surname], [email]');
        $createdAt = DateTimeField::new('createdAt', $this->translator->trans('common_areas.created_at', [], 'messages', $this->getUser()->getLocale()));
        $sentAt    = DateTimeField::new('sentAt', $this->translator->trans('emailing.configureFields.sent_at', [], 'messages', $this->getUser()->getLocale()));
        $status    = ChoiceField::new('status')->setChoices($this->getStatuses())
            ->setLabel($this->translator->trans('emailing.configureFields.status', [], 'messages', $this->getUser()->getLocale()));

        $recipients = AssociationField::new('recipients');

        if (in_array($pageName, [Crud::PAGE_INDEX, Crud::PAGE_DETAIL]))
        {
            return [$subject, $createdAt, $status, $sentAt, $recipients];
        }
        else if (in_array($pageName, [Crud::PAGE_NEW, Crud::PAGE_EDIT]))
        {
            return [$subject, $body];
        }
    }


    public function configureFilters (Filters $filters): Filters
    {
        $filters
            ->add(ChoiceFilter::new('status')->setChoices($this->getStatuses()));

        return $filters;
    }


    public function configureResponseParameters (KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName'))
        {

            $emailTemplate = $this->context->getContext()->getEntity()->getInstance();

            $responseParameters->set('emailTemplate', $emailTemplate);
        }

        return $responseParameters;
    }


    protected function getStatuses ()
    {
        return [
            $this->translator->trans('emailing.status.pending', [], 'messages', $this->getUser()->getLocale()) => EmailTemplate::EMAIL_STATUS_PENDING,
            $this->translator->trans('emailing.status.sending', [], 'messages', $this->getUser()->getLocale()) => EmailTemplate::EMAIL_STATUS_SENDING,
            $this->translator->trans('emailing.status.sent', [], 'messages', $this->getUser()->getLocale())    => EmailTemplate::EMAIL_STATUS_SENT,
        ];
    }


    public function sendEmail (AdminContext $context)
    {
        $centerRepository               = $this->em->getRepository(Center::class);
        $professionalCategoryRepository = $this->em->getRepository(ProfessionalCategory::class);
        $departmentRepository           = $this->em->getRepository(Department::class);

        $countries = array_flip($this->settings->get('app.user.extrafields')['country']['options']['choices']);
        $countries = array_map(function ($key, $name) {
            return ['id' => $key, 'name' => $name];
        }, array_keys($countries), $countries);

        $centers = array_map(function ($center) {
            return ['id' => $center->getId(), 'name' => $center->getName()];
        }, $centerRepository->findBy([], ['name' => 'ASC']));

        $departments = array_map(function ($center) {
            return ['id' => $center->getId(), 'name' => $center->getName()];
        }, $departmentRepository->findBy([], ['name' => 'ASC']));

        $professionalCategories = array_map(function ($category) {
            return ['id' => $category->getId(), 'name' => $category->getFullName()];
        }, $professionalCategoryRepository->findBy([], ['name' => 'ASC']));


        return $this->render('admin/emailing/send.html.twig', [
            'countries'              => $countries,
            'centers'                => $centers,
            'departments'            => $departments,
            'professionalCategories' => $professionalCategories,
            'email'                  => $this->context->getContext()->getEntity()->getInstance(),
        ]);
    }


    /**
     * @Route("/admin/emailing/send", name="admin_emailing_send")
     * @param Request $request
     * @return Response
     */
    public function send (Request $request)
    {
        $content = json_decode($request->getContent(), true);

        if ($content['recipients'] == 'test')
        {
            return $this->sendTestEmail($content);
        }
        else if ($content['recipients'] == 'users')
        {
            return $this->addRecipientsToQueue($content);
        }
    }


    protected function sendTestEmail (array $request)
    {
        $status = Response::HTTP_OK;
        $error  = false;
        $data   = '';


        $testEmails = array_map('trim', explode(',', $request['testEmails']));

        $validEmails = true;
        foreach ($testEmails as $testEmail)
        {
            if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL))
            {
                $validEmails = false;
            }
        }

        if (!$validEmails)
        {
            $status = Response::HTTP_BAD_REQUEST;
            $error  = true;
            $data   = 'Error in emails';
        }
        else
        {
            $email = $this->em->find('App:EmailTemplate', $request['email']);

            $templatedEmail = (new TemplatedEmail())
                ->from(new Address($this->settings->get('app.fromEmail'), $this->settings->get('app.fromName')))
                ->subject($email->getSubject())
                ->htmlTemplate('template_email/custom.html.twig')
                ->context([
                    'content' => $email->getBody(),
                ]);

            foreach ($testEmails as $testEmail)
            {
                $templatedEmail->addTo(new Address($testEmail));
            }

            $this->mailer->send($templatedEmail);

            $data = $this->translator->trans('emailing.test_email_sent', [], 'messages', $this->getUser()->getLocale());
            $this->addFlash('success', $data);
        }


        $response = [
            'status' => $status,
            'error'  => $error,
            'data'   => $data,
        ];

        return $this->sendResponse($response);
    }


    protected function addRecipientsToQueue (array $request)
    {
        $status = Response::HTTP_OK;
        $error  = false;
        $data   = '';

        $response = [
            'status' => $status,
            'error'  => $error,
            'data'   => $data,
        ];

        /**
         * @var EmailTemplate $email
         */
        $email = $this->em->find('App:EmailTemplate', $request['email']);

        $categories  = array_column($request['filters']['categories'], 'id');
        $departments = array_column($request['filters']['departments'], 'id');
        $centers     = array_column($request['filters']['centers'], 'id');
        $countries   = array_column($request['filters']['countries'], 'id');

        $filters = compact('categories', 'departments', 'centers', 'countries');

        $userRepository = $this->em->getRepository(User::class);
        $users          = $userRepository->findFiltered($filters);

        $date = new \DateTime($request['sendAt']);

        if (count($users))
        {
            foreach ($users as $user)
            {
                $emailRecipient = new EmailRecipient();
                $emailRecipient
                    ->setTemplate($email)
                    ->setUser($user)
                    ->setCreatedAt(new \DateTime())
                    ->setStatus(EmailRecipient::EMAIL_STATUS_PENDING);
                $this->em->persist($emailRecipient);
            }

            $sentAt = $request['sendAt'] ? new \DateTime($request['sendAt']) : new \DateTime();
            $email->setStatus(EmailTemplate::EMAIL_STATUS_SENDING)
                ->setSentAt($sentAt);
            $this->em->persist($email);

            $data = $this->translator->trans('emailing.email_on_queue', [], 'messages', $this->getUser()->getLocale());
            $this->addFlash('success', $data);
        }

        $this->em->flush();

        return $this->sendResponse($response);
    }


    /**
     * @Route("/admin/emailing/recipients/{id}/{page}", name="admin_emailing_recipients_with_page")
     * @param EmailTemplate $emailTemplate
     * @param int $page
     * @param EmailRecipientRepository $emailRecipientRepository
     * @param Request $request
     * @param PaginatorInterface $paginator
     * @return Response
     */
    public function recipients (EmailTemplate $emailTemplate, int $page = 1, EmailRecipientRepository $emailRecipientRepository, Request $request, PaginatorInterface $paginator)
    {
        $q = $request->query->get('q');

        $queryBuilder = $emailRecipientRepository->getWithSearchQueryBuilder($emailTemplate, $q);

        $itemsPerPage = $this->settings->get('app.emailing.paginationItemsPerPage');
        $pagination = $paginator->paginate(
            $queryBuilder, /* query NOT result */
            $page, /*page number*/
            $itemsPerPage,/*limit per page*/
        );
        $countPages = ceil($pagination->getTotalItemCount() / $itemsPerPage);

        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => [
                'pagination' => [
                    'currentPageNumber' => $pagination->getCurrentPageNumber(),
                    'numItemsPerPage' => $pagination->getItemNumberPerPage(),
                    'totalCount' => $pagination->getTotalItemCount(),
                    'countPages' => $countPages
                ],
                'recipients' => $pagination,
            ],
        ];

        return $this->sendResponse($response, [
            'groups'                     => ['emailing'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            }]);
    }
}
