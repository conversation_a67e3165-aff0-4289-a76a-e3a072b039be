<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\Chapter;
use App\Entity\RoleplayProject;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Entity\UserRoleplayProject;
use App\Service\SettingsService;
use App\Service\Vimeo\VimeoService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;

class RoleplayProjectController extends AbstractController
{
    use SerializerTrait;

    protected RequestStack $requestStack;
    protected Request $request;
    private EntityManagerInterface $em;
    private VimeoService $vimeoService;
    protected SettingsService $settings;

    public function __construct(
        EntityManagerInterface $em,
        VimeoService $vimeoService,
        RequestStack $requestStack,
        SettingsService $settingsService
    ) {
        $this->em = $em;
        $this->vimeoService = $vimeoService;
        $this->requestStack = $requestStack;
        $this->request = $requestStack->getCurrentRequest();
        $this->settings = $settingsService;
    }

    public function saveTime(RoleplayProject $project, Request $request): Response
    {
        $timeData = json_decode($request->getContent(), true);

        $userProject = $this->em->getRepository(UserRoleplayProject::class)
            ->getByUserAndProject($this->getUser(), $project);

        $userCourseChapter = $userProject->getUserCourseChapter();
        $userCourseChapter->setTimeSpent($userCourseChapter->getTimeSpent() + $timeData['time']);

        $this->em->flush();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [],
        ];

        return $this->sendResponse($response);
    }

    public function removeFile(RoleplayProject $project, Request $request): Response
    {
        $requesData = json_decode($request->getContent(), true);
        $url = $requesData['resourcePath'];

        $parsedUrl = parse_url($url);
        $path = $parsedUrl['path'];
        $pathWithoutFirstSlash = ltrim($path, '/');

        if ($this->isVimeoUrl($url)) {
            $this->removeVideoVimeo($url);
        } elseif ($this->isValidUrl($url)) {
            $this->removeResourceImage($pathWithoutFirstSlash);
        } else {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'La url no es válida',
            ]);
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [],
        ];

        return $this->sendResponse($response);
    }

    private function isVimeoUrl($url)
    {
        $patron = '/^(https?:\/\/)?(www\.)?vimeo\.com\/(\d+)/i';

        return preg_match($patron, $url);
    }

    private function removeVideoVimeo($url)
    {
        $videoId = $this->fetchIdVimeo($url);
        $idFolderVimeo = $this->settings->get('app.projectIdRoleplay');
        $this->vimeoService->deleteFromVimeo($videoId, $idFolderVimeo);
    }

    private function fetchIdVimeo($url)
    {
        $patron = '/vimeo\.com\/(\d+)/';
        preg_match($patron, $url, $coincidencias);

        return $coincidencias[1] ?? null;
    }

    private function isValidUrl($url): bool
    {
        $pathRoleplay = $this->settings->get('roleplay_uploads_path');
        $pathRoleplayPattern = preg_quote($pathRoleplay, '/');
        $pattern = '/^https?:\/\/.*' . $pathRoleplayPattern . '/i';

        return (bool) preg_match($pattern, $url);
    }

    private function removeResourceImage($path)
    {
        if (file_exists($path)) {
            $success = unlink($path);

            if (!$success) {
                throw $this->createNotFoundException("Cannot delete $path");
            }
        }
    }

    public function saveResource(RoleplayProject $project, Request $request): Response
    {
        try {
            $file = $request->files->get('file');

            if (!$file) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'No se ha enviado ningún archivo',
                ]);
            }
            // Si es un video
            if (false !== strpos($file->getMimeType(), 'video')) {
                $response = $this->uploadVideoVimeo($file);
            } elseif (false !== strpos($file->getMimeType(), 'image')) {
                $response = $this->uploadImage($file);
            } else {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'El archivo no es un video o una imagen',
                ]);
            }
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ];
        }

        return $this->sendResponse($response);
    }

    private function uploadVideoVimeo($file)
    {
        $folderInVimeo = $this->settings->get('app.projectIdRoleplay');
        $result = $this->vimeoService->upload($file, $folderInVimeo);
        if (Response::HTTP_OK !== $result['status']) {
            return [
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => 'Fallo al subir el archivo',
            ];
        }

        if ($result) {
            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'videoId' => $result['videoId'],
                    'url' => $result['link'],
                ],
            ];
        }
    }

    private function uploadImage($file): array
    {
        $fileName = md5(uniqid()) . '.' . $file->guessExtension();
        $file->move(
            $this->settings->get('app.roleplay.image_uploads_path'),
            $fileName
        );

        $url = $this->request->getUriForPath(
            DIRECTORY_SEPARATOR
            . $this->settings->get('app.roleplay.image_uploads_path')
            . '/'
            . $fileName
        );

        return [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'url' => $url,
            ],
        ];
    }

    private function fetchUserCourseChapter(Chapter $chapter): UserCourseChapter
    {
        return $this->em->getRepository(UserCourseChapter::class)->findOneBy([
            'userCourse' => $this->fetchUserCourse($chapter),
            'chapter' => $chapter,
        ]) ?? $this->createUserCourseChapter($chapter);
    }

    private function fetchUserCourse(Chapter $chapter)
    {
        $announcement = $this->findAnnouncement($chapter);

        return $this->em->getRepository(UserCourse::class)->findOneBy([
            'user' => $this->getUser(),
            'course' => $chapter->getCourse(),
            'announcement' => ($announcement) ?: null,
        ]) ?? $this->createUserCourse($chapter);
    }

    private function findAnnouncement(Chapter $chapter)
    {
        return $this->em->getRepository(Announcement::class)
            ->findAnnouncementUserNotified($chapter->getCourse(), $this->getUser());
    }

    private function createUserCourse(Chapter $chapter): UserCourse
    {
        $announcement = $this->findAnnouncement($chapter);

        $userCourse = (new UserCourse())
            ->setUser($this->getUser())
            ->setCourse($chapter->getCourse());

        if ($announcement) {
            $userCourse->setAnnouncement($announcement);
        }

        $this->em->persist($userCourse);
        $this->em->flush();

        return $userCourse;
    }

    private function createUserCourseChapter(Chapter $chapter): UserCourseChapter
    {
        $userCourseChapter = (new UserCourseChapter())
            ->setUserCourse($this->fetchUserCourse($chapter))
            ->setChapter($chapter)
            ->setTimeSpent(0);

        $this->em->persist($userCourseChapter);
        $this->em->flush();

        return $userCourseChapter;
    }

    private function updateTimeChapter($data, UserCourseChapter $userCourseChapter): void
    {
        if (isset($data['time']) || isset($data['data']['time'])) {
            $time = (isset($data['time'])) ? $data['time'] : $data['data']['time'];
            $userCourseChapter->setTimeSpent($userCourseChapter->getTimeSpent() + $time);
        }
    }
}
