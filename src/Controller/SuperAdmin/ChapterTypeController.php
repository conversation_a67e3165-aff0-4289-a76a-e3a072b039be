<?php

namespace App\Controller\SuperAdmin;

use App\Admin\Traits\SerializerTrait;
use App\Entity\ChapterType;
use App\Entity\ChapterTypeTranslation;
use App\Repository\ChapterTypeRepository;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Service\SettingsService;


/**
 * @Route("/admin/")
 */
class ChapterTypeController extends AbstractCrudController
{
    use SerializerTrait;

    private EntityManagerInterface $em;
    private SettingsService $settings;


    public function __construct(EntityManagerInterface $em, SettingsService $settingsService)
    {
        $this->em = $em;
        $this->settings = $settingsService;

    }

    public static function getEntityFqcn(): string
    {
        return ChapterType::class;
    }

    /**
     * @Rest\Get("chapter-type/all")
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @param ChapterTypeRepository $chapterTypeRepository
     * @return Response
     */
    public function all(ChapterTypeRepository $chapterTypeRepository): Response
    {
        $data = [];

        /** @var ChapterType $chapterType */
        foreach ($chapterTypeRepository->findAll() as $chapterType) {
            $data[] = [
                'id' => $chapterType->getId(),
                'name' => $this->getChapterTypeNameByLocale($chapterType),
                'type' => $chapterType->getType(),
                'active' => $chapterType->isActive(),
                'video' =>  $chapterType->getVideo(),
                'videoEn' => $chapterType->getVideoEn(),
                'translations' => $this->getChapterTypeTranslations($chapterType),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    private function getChapterTypeNameByLocale(ChapterType $chapterType){
        /** @var User $user */
        $user = $this->getUser();
        $locale = $user->getLocale() ?? $this->settings->get('app.adminDefaultLanguage');
        
        $chapterTypeTranslation = $this->em->getRepository(ChapterTypeTranslation::class)->findOneBy(["translatable" => $chapterType, "locale" => $locale]);

        return $chapterTypeTranslation->getName() ?? $chapterType->getName();
    }


    private function getChapterTypeTranslations(ChapterType $chapterType): array
    {
        $chapterTypeTranslations = [];
        foreach ($this->settings->get('app.languages') as $locale) {
            $translation = $this->em->getRepository(ChapterTypeTranslation::class)->findOneBy([
                'translatable' => $chapterType,
                'locale' => $locale,
            ]);
            if($translation){
                $elemento = new \stdClass();
                $elemento->locale = $locale;
                $elemento->name = $translation->getName();
                $elemento->description = $translation->getDescription();
                array_push($chapterTypeTranslations, $elemento);
            }
        }        

        return $chapterTypeTranslations;
    }


    /**
     * @IsGranted("ROLE_SUPER_ADMIN") 
     * @Rest\Post("chapter-type/{id}/active")
     * @param Request $request
     * @param ChapterType $chapterType
     * @return Response
     */
    public function changeActiveStatus(Request $request, ChapterType $chapterType): Response {
        $content = json_decode($request->getContent(), true);
        $active = $content['active'] ?? false;
        $chapterType->setActive($active);
        $this->em->persist($chapterType);
        $this->em->flush();
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle chapter type information
     * @Rest\Post("chapter-type/update")
     * @param Request $request
     * @return Response
     */
    public function updateChapterType(Request $request): Response
    {
        $content = json_decode($request->getContent(), true);       
        $id = $content['id'] ?? -1;
        $chapterType = $this->em->getRepository(ChapterType::class)->find($id);
        if (!$chapterType) return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => true,
            'data' => 'NOT_FOUND'
        ]);
        if (($result = $this->saveChapterType($request, $chapterType)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    public function saveChapterType(Request $request, ChapterType $chapterType) {
        $content = json_decode($request->getContent(), true);
        $name = $content['name'] ?? null;
        $type = $content['type'] ?? null;
        $active = $content['active'] ?? false;
        $video = $content['video'] ?? null;
        $videoEn = $content['videoEn'] ?? null;
        $translations = $content['translations'] ?? null;
        $errors = [];

        if (empty($name)) $errors[] = 'Name required';
        if (count($errors)) return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => true,
            'data' => $errors
        ]);
        $chapterType->setType($type)
            ->setActive($active)
            ->setVideo($video)
            ->setVideoEn($videoEn)
        ;

        $this->em->persist($chapterType);
        $this->saveChapterTypeTranslation($translations, $chapterType);
        $this->em->flush();
        return true;
    }


    private function saveChapterTypeTranslation(array $translations, ChapterType $chapterType): void
    {
        try {
            foreach ($translations as $data) {
                $translation = $this->em->getRepository(ChapterTypeTranslation::class)->findOneBy([
                    'translatable' => $chapterType,
                    'locale' => $data['locale'],
                ]);

                $name = $data['name'] ?? null;
                $description = $data['description'] ?? null;

                if (empty($name) && empty($description)) {
                    if ($translation) {
                        $this->em->remove($translation);
                    }
                    continue;
                }

                if (!$translation) {
                    $translation = new ChapterTypeTranslation();
                    $translation->setTranslatable($chapterType);
                    $translation->setLocale($data['locale']);
                }

                $translation->setName($name)
                    ->setDescription($description);
                $this->em->persist($translation);
            }

            $this->em->flush();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

    }

}
