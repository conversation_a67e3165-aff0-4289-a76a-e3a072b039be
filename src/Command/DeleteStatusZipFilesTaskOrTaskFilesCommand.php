<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\Task;
use App\Entity\ZipFileTask;
use App\Service\Task\TaskService;
use App\Service\TemplatedEmail\TemplatedEmailService;
use App\Service\ZipFileTask\ZipFileTaskService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class DeleteStatusZipFilesTaskOrTaskFilesCommand extends Command
{
    private EntityManagerInterface $em;
    private ZipFileTaskService $zipFileTaskService;
    private TaskService $taskService;
    private TemplatedEmailService $templatedEmailService;
    private LoggerInterface $logger;

    public function __construct(
        EntityManagerInterface $em,
        ZipFileTaskService $zipFileTaskService,
        TaskService $taskService,
        TemplatedEmailService $templatedEmailService,
        LoggerInterface $logger
    ) {
        $this->em = $em;
        $this->zipFileTaskService = $zipFileTaskService;
        $this->taskService = $taskService;
        $this->templatedEmailService = $templatedEmailService;
        $this->logger = $logger;

        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('delete:status:zip-excel')
            ->setDescription('Those records whose status is this process or an error has occurred will be deleted.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->text('[delete:status:zip-excel] ');

        try {
            $exportTasks = $this->taskService->getExpiredTaskWithExport();
            $io->text(\sprintf('%d Task-withExport to remove | ', \count($exportTasks)));

            $tasksWithoutExport = $this->taskService->getExpiredTaskWithoutExport();
            $io->text(\sprintf('%d Task-withoutExport to remove | ', \count($tasksWithoutExport)));

            $zipFiles = $this->zipFileTaskService->getZipFilesTaskExpiredOrFail();
            $io->text(\sprintf('%d ZipFileTasks to remove // ', \count($zipFiles)));

            $this->processZombieTasks($exportTasks, $tasksWithoutExport, $zipFiles, $io);
            $io->success('processZombieTasks Finished.');

            return Command::SUCCESS;
        } catch (\Throwable $e) {
            $this->templatedEmailService->sendErrorNotification(
                'DeleteStatusZipFilesTaskOrTaskFilesCommand',
                $e,
                null
            );

            $io->text('FAILURE | ');

            return Command::FAILURE;
        }
    }

    private function processZombieTasks(array $exportTasks, array $tasksWithoutExport, array $zipFiles, SymfonyStyle $io): void
    {
        // 1) Task with Export
        foreach ($exportTasks as $exportObj) {
            $task = $exportObj->getTask();
            if (!$task) {
                continue;
            }

            if (-1 === $task->getStatus()) {
                // // disabled in the query that collects the elements getExpiredTaskWithExport
                // $this->em->remove($exportObj);
                // $io->text("Removing Task-withExport_ID {$task->getId()} | ");
            } elseif (1 === $task->getStatus()) {
                $task->setStatus(Task::TASK_TIMEOUT);
                $io->text("Marking_Timeout Task-withExport_ID {$task->getId()} | ");
                $this->templatedEmailService->sendZombieNotification('Task (with Export)', $task);
            }
        }
        $this->em->flush();

        // 2) Task without Export
        foreach ($tasksWithoutExport as $task) {
            if (-1 === $task->getStatus()) {
                // // disabled in the query that collects the elements getExpiredTaskWithoutExport
                // $this->em->remove($task);
                // $io->text("Removing Task-withoutExport_ID {$task->getId()} | ");
            } elseif (1 === $task->getStatus()) {
                $task->setStatus(Task::TASK_TIMEOUT);
                $io->text("Marking_Timeout Task-withoutExport_ID {$task->getId()} | ");
                $this->templatedEmailService->sendZombieNotification('Task (without Export)', $task);
            }
        }
        $this->em->flush();

        // 3) ZipFileTask
        foreach ($zipFiles as $zipFile) {
            if (-1 === $zipFile->getStatus()) {
                // // disabled in the query that collects the elements getZipFilesTaskExpiredOrFail
                // $zipFile->setDeletedAt(new \DateTimeImmutable());
                // $io->text("Removing ZipFileTask_ID {$zipFile->getId()} | ");
            } elseif (1 === $zipFile->getStatus()) {
                $zipFile->setStatus(ZipFileTask::STATUS_TIMEOUT);
                $io->text("Marking_Timeout ZipFileTask_ID {$zipFile->getId()} | ");
                $this->templatedEmailService->sendZombieNotification('ZipFileTask', $zipFile);
            }
            $this->em->persist($zipFile);
        }
        $this->em->flush();
    }
}
