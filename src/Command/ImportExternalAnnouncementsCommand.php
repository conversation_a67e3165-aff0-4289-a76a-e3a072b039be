<?php

declare(strict_types=1);

namespace App\Command;

use App\Repository\ExternalAnnouncement\ExternalAnnouncementCriteria;
use App\Repository\ExternalAnnouncement\ExternalAnnouncementRepositories;
use App\Repository\ExternalAnnouncement\ExternalAnnouncementRepository;
use App\Repository\ExternalAnnouncement\ExternalAnnouncementRepositoryException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ImportExternalAnnouncementsCommand extends Command
{
    protected static $defaultName = 'app:import-external-announcements';
    protected static $defaultDescription = 'Import external announcements';

    public function __construct(
        private readonly ExternalAnnouncementRepositories $externalAnnouncementRepositories
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'file',
                null,
                InputOption::VALUE_REQUIRED,
                'The path to the file with the announcements to import'
            )
            ->addOption(
                'from',
                null,
                InputOption::VALUE_REQUIRED,
                'The date from which to import the announcements'
            )
            ->addOption(
                'to',
                null,
                InputOption::VALUE_REQUIRED,
                'The date to which to import the announcements'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $filePath = $input->getOption('file');
        $from = $input->getOption('from');
        $to = $input->getOption('to');

        if (!$filePath && !$from && !$to) {
            $output->writeln('You must specify at least one of the following options: file, from, to');

            return Command::FAILURE;
        }

        try {
            $externalAnnouncementRepository
                = $this->externalAnnouncementRepositories->getExternalAnnouncementRepositoryByClient('Iberostar');
        } catch (ExternalAnnouncementRepositoryException $e) {
            $output->writeln($e->getMessage());

            return Command::FAILURE;
        }

        if (
            $filePath
            && ExternalAnnouncementRepository::TYPE_FILE !== $externalAnnouncementRepository->getType()
        ) {
            $output->writeln('This repository does not support file import');

            return Command::FAILURE;
        }

        if (
            (
                $from
                || $to
            ) && ExternalAnnouncementRepository::TYPE_API !== $externalAnnouncementRepository->getType()
        ) {
            $output->writeln('This repository does not support date filtering');

            return Command::FAILURE;
        }

        $output->writeln('Importing external announcements...');

        if ($filePath) {
            $announcements = $externalAnnouncementRepository->getAnnouncementsByFile($filePath);
        } else {
            $externalAnnouncementCriteria = new ExternalAnnouncementCriteria();

            try {
                if ($from) {
                    $externalAnnouncementCriteria->setFrom(new \DateTimeImmutable($from));
                }

                if ($to) {
                    $externalAnnouncementCriteria->setTo(new \DateTimeImmutable($to));
                }
            } catch (\Exception $e) {
                $output->writeln($e->getMessage());

                return Command::FAILURE;
            }

            $announcements = $externalAnnouncementRepository->getAnnouncements($externalAnnouncementCriteria);
        }

        // TODO Save the announcements in the database

        return Command::SUCCESS;
    }
}
