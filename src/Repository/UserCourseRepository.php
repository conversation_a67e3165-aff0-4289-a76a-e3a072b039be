<?php

declare(strict_types=1);

namespace App\Repository;

use App\Admin\Traits\StatsQueryFiltersTrait;
use App\Admin\Traits\UserCourseTrait;
use App\Admin\Traits\UserManagerTrait;
use App\Entity\Course;
use App\Entity\Filter;
use App\Entity\Itinerary;
use App\Entity\ItineraryCourse;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Entity\UserExtra;
use App\Enum\CourseStatsEnum;
use App\Service\Course\DT0\UserCourseStatsDTO;
use App\Service\SettingsService;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method UserCourse|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserCourse|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserCourse[]    findAll()
 * @method UserCourse[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserCourseRepository extends ServiceEntityRepository
{
    use StatsQueryFiltersTrait;
    use UserCourseTrait;
    use UserManagerTrait;
    private SettingsService $settings;

    public function __construct(ManagerRegistry $registry, SettingsService $settings)
    {
        $this->settings = $settings;
        parent::__construct($registry, UserCourse::class);
    }

    public function countByUser(User $user, $finished = null)
    {
        $query = $this->createQueryBuilder('c')
            ->select('count(c.id)')
            ->andWhere('c.user = :user')
            ->setParameter('user', $user);
        if (!\is_null($finished)) {
            $query->andWhere('c.finishedAt IS ' . ($finished ? 'NOT' : '') . ' NULL');
        }

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function countByCourse(Course $course, ?bool $finished = null)
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id)')
            ->join('uc.course', 'c')
            ->andWhere('uc.course = :course')
            ->orWhere('c.translation = :course')
            ->join('uc.user', 'u')
            ->setParameter('course', $course)
            ->andWhere('uc.startedAt IS NOT NULL');
        if (null !== $finished) {
            $query->andWhere('uc.finishedAt IS ' . ($finished ? 'NOT' : '') . ' NULL');
        }

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function finishedCourseByUser($user, $conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('COUNT(DISTINCT uc.id) as count')
            ->andWhere('uc.user = :user')
            ->andWhere('uc.finishedAt IS NOT NULL')
            ->setParameter('user', $user);

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getUserCourseTranslated(Course $course, User $user): ?UserCourse
    {
        $query = $this->createQueryBuilder('uc')
            ->select('uc')
            ->join('uc.course', 'c')
            ->andWhere('uc.user = :user')
            ->andWhere('uc.course = :course OR c.translation = :course')
            ->andWhere('uc.announcement IS NULL')
            ->setParameter('user', $user)
            ->setParameter('course', $course)
            ->setMaxResults(1);

        return $query->getQuery()
            ->getOneOrNullResult();
    }

    public function getDailyFinishedCourses(array $conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select(['count(uc.id) as count', "DATE_FORMAT(uc.finishedAt, '%Y-%m-%d') as date"])
            ->andWhere('uc.finishedAt IS NOT NULL')
            ->groupBy('date');

        $this->setFinishedCoursesFilters($query, $conditions);

        return $query->getQuery()->getResult();
    }

    public function getTotalFinishedCourses(array $conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select(['count(uc.id)'])
            ->andWhere('uc.finishedAt IS NOT NULL');

        $this->setFinishedCoursesFilters($query, $conditions);

        return $query->getQuery()->getSingleScalarResult();
    }

    private function setFinishedCoursesFilters($query, $conditions)
    {
        /*  if (isset($conditions['active']) || isset($conditions['country']) || isset($conditions['center']) || !empty($conditions['category']) || !empty($conditions['departament']) || !empty($conditions['gender']) || !empty($conditions['division']) || !empty($conditions['filters'])) {
            $query->leftJoin('uc.user', 'u')
                ->leftJoin('u.extra', 'ue');
        } */

        $this->setSearchFilters($query, 'uc', $conditions);
        // $this->setQueryFilters($query, $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.finishedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.finishedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }
    }

    public function getRankingCourses(array $conditions = [], $order)
    {
        $query = $this->createQueryBuilder('uc')
            ->select(['count(uc.user) as count', 'c.id as course', 'c.name'])
            ->leftJoin('uc.course', 'c')
            ->andWhere("c.name != '' ")
            ->andWhere('uc.finishedAt IS NOT NULL');

        $this->getStudentRankingCourse($query, $conditions);

        $query->groupBy('uc.course');
        $query->orderBy('count', $order);

        $query->setMaxResults(4);

        return $query->getQuery()->getResult();
    }

    public function getStudentRankingCourse($query, $conditions)
    {
        if (isset($conditions['active']) || isset($conditions['country']) || isset($conditions['center']) || !empty($conditions['category']) || !empty($conditions['departament']) || !empty($conditions['gender']) || !empty($conditions['division']) || !empty($conditions['filters'])) {
            $query->leftJoin('uc.user', 'u')
                ->leftJoin('u.extra', 'ue');
        }

        $this->setQueryFilters($query, $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.finishedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.finishedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }
    }

    public function findExport($conditions, User $user)
    {
        $this->_em->getFilters()->disable('softdeleteable');

        $query = $this->createQueryBuilder('uc');

        $subquery = $this->_em->createQueryBuilder()
            ->select('SUM(uccSum.timeSpent)')
            ->from('App:UserCourseChapter', 'uccSum')
            ->andWhere('uccSum.userCourse = uc.id')
            ->andWhere('uccSum.finishedAt IS NOT NULL');

        $subqueryUserCourse = $this->_em->createQueryBuilder()
            ->select('IDENTITY(ucc.userCourse)')
            ->from(UserCourseChapter::class, 'ucc')
            ->andWhere('ucc.userCourse = uc.id')
            ->andWhere('ucc.finishedAt IS NOT NULL');

        if (\array_key_exists('startDate', $conditions) && !empty($conditions['startDate'])) {
            $subquery->andWhere('uccSum.finishedAt >= :start');
            $subqueryUserCourse->andWhere('ucc.finishedAt >= :start');

            // course must be started after start date
            if (\array_key_exists('courseStartedIntime', $conditions) && $conditions['courseStartedIntime'] && 1 == $conditions['courseStartedIntime']) {
                $subquery->andWhere('uc.startedAt >= :start');
                $subqueryUserCourse->andWhere('uc.startedAt >= :start');
            }
            // course must be finished after start date
            if (\array_key_exists('courseFinishedIntime', $conditions) && $conditions['courseFinishedIntime'] && 1 == $conditions['courseFinishedIntime']) {
                $subquery->andWhere('uc.finishedAt >= :start');
                $subqueryUserCourse->andWhere('uc.finishedAt >= :start');
            }
        }

        if (\array_key_exists('endDate', $conditions) && !empty($conditions['endDate'])) {
            $subquery->andWhere('uccSum.finishedAt <= :end');
            $subqueryUserCourse->andWhere('ucc.finishedAt <= :end');

            // course must be started before end date
            if (\array_key_exists('courseStartedIntime', $conditions) && !empty($conditions['courseStartedIntime'])) {
                if (1 == $conditions['courseStartedIntime']) {
                    $subquery->andWhere('uc.startedAt <= :end');
                    $subqueryUserCourse->andWhere('uc.startedAt <= :end');
                }
            }
            // course must be finished before end date
            if (\array_key_exists('courseFinishedIntime', $conditions) && !empty($conditions['courseFinishedIntime'])) {
                if (1 == $conditions['courseFinishedIntime']) {
                    $subquery->andWhere('uc.finishedAt <= :end');
                    $subqueryUserCourse->andWhere('uc.finishedAt <= :end');
                }
            }
        }

        if (\array_key_exists('courseFinishedIntime', $conditions) && !empty($conditions['courseFinishedIntime'])) {
            if (1 == $conditions['courseFinishedIntime']) {
                $subquery->andWhere('uc.finishedAt IS NOT NULL'); // course must be finished completely
                $subqueryUserCourse->andWhere('uc.finishedAt IS NOT NULL'); // course must be finished completely
            }
        }

        $queryColumns = [
            'u.id AS userId',
            'uc.id AS userCourseId',
            'CONCAT(u.firstName, \' \', u.lastName) as userName',
            'u.email',
            'ex.birthdate as birthdate',
            'ex.gender as gender',
            'c.id AS courseId',
            'c.name AS courseName',
            'cc.name AS courseCategoryName',
            'ac.email AS announcementCreator',
            'com.name AS companyName',
            'uc.startedAt',
            'uc.finishedAt',
            'uc.valuedAt',
            '(' . $subquery->getDQL() . ') as timeSpent',
            'u.registerKey AS nif',
            'JSON_EXTRACT(u.meta, \'$.HRP\') AS hrp',
            'JSON_EXTRACT(u.meta, \'$.employeer\') AS employeer',
        ];

        $query->select($queryColumns)
            ->leftJoin('uc.course', 'c')
            ->leftJoin('c.category', 'cc')
            ->leftJoin('uc.announcement', 'a')
            ->leftJoin('a.createdBy', 'ac')
            ->leftJoin('uc.user', 'u')
            ->leftJoin('u.extra', 'ex')
            ->leftJoin('ex.category', 'pc')
            ->leftJoin('pc.parent', 'pcp')
            ->leftJoin('ex.department', 'd')
            ->leftJoin('ex.center', 'ce')
            ->leftJoin('u.userFieldsFundae', 'ucf')
            ->leftJoin('ucf.userCompany', 'com')
            // ->andWhere('u.deletedAt IS  NULL')
            ->andWhere($query->expr()->exists($subqueryUserCourse->getDQL()))
            ->orderBy('uc.startedAt', 'ASC');

        if (isset($conditions['activeUsers']) && $conditions['activeUsers'] && '' != $conditions['activeUsers']) {
            $query->andWhere('u.isActive = :active');
            $query->setParameter('active', $conditions['activeUsers']);
            /* AND u0_.is_active = 1 */
        }

        if (\array_key_exists('startDate', $conditions) && !empty($conditions['startDate'])) {
            $starDateTime = \DateTime::createFromFormat('Y-m-d H:i', $conditions['startDate'] . ' 00:00');
            $query->setParameter('start', $starDateTime);
            // echo "startDate" .  $starDateTime->format('Y-m-d H:i');
        }

        if (\array_key_exists('endDate', $conditions) && !empty($conditions['endDate'])) {
            $endDateTime = \DateTime::createFromFormat('Y-m-d H:i', $conditions['endDate'] . ' 23:59');
            $query->setParameter('end', $endDateTime);
            // echo "endDate" .  $endDateTime->format('Y-m-d H:i');
        }

        if (isset($conditions['country']) || isset($conditions['center']) || isset($conditions['category']) || isset($conditions['departament']) || !empty($conditions['division'])) {
            $query->leftJoin('u.extra', 'ue');
        }

        $filtersArray = [];

        if (!empty($conditions['customFilters']) && ',' != $conditions['customFilters']) {
            $filtersId = explode(',', $conditions['customFilters']);

            foreach ($filtersId as $filterValue) {
                $filter = $this->_em->getRepository(Filter::class)->find($filterValue);
                if ($filter) {
                    $filtersArray[$filter->getFilterCategory()->getId()][] = $filterValue;
                }
            }
        }

        if ($user->isManager()) {
            $query = $this->getAssignedUsersByFilters($query, $user);
        }

        if (!empty($filtersArray)) {
            foreach ($filtersArray as $category_id => $filters) {
                $query->innerJoin('u.filter', 'f' . $category_id);
                $query->andWhere($query->expr()->in('f' . $category_id . '.id', $filters));
            }
        }

        $this->getExportConditional($conditions, $query);

        $this->setQueryFilters($query, $conditions);

        $result = $query->getQuery()->getResult();
        $this->_em->getFilters()->enable('softdeleteable');

        return $result;
    }

    private function getExportConditional($conditions, $query)
    {
        if (isset($conditions['center']) && !empty($conditions['center'])) {
            $query->andWhere('ue.center IN (:center)')
                ->setParameter('center', $conditions['center']);
        }

        if (isset($conditions['category']) && !empty($conditions['category'])) {
            $query->andWhere('ue.category = pc.id')
                ->andWhere('pc.parent = :parent')
                ->setParameter('parent', $conditions['category']);
        }

        if (isset($conditions['departament']) && !empty($conditions['departament'])) {
            $query->andWhere('ue.department IN (:departament)')
                ->setParameter('departament', $conditions['departament']);
        }

        if (isset($conditions['gender']) && !empty($conditions['gender'])) {
            $query->andWhere('ue.gender IN (:gender)')
                ->setParameter('gender', $conditions['gender']);
        }
    }

    public function exportCourseInfo($conditions, User $user)
    {
        $query = $this->createQueryBuilder('uc');

        $subquery = $this->_em->createQueryBuilder()
            ->select('SUM(ucc.timeSpent)')
            ->from('App:UserCourseChapter', 'ucc')
            ->where('ucc.userCourse = uc.id')
            ->getDQL();

        $query->select([
            'u.id',
            'c.id as courseId',
            'u.firstName as userName',
            'u.lastName as userLastName',
            'u.email',
            /* 'pc.name as category',
            'center.name as center',
            'd.name as departament',
            'ue.country as country',
            'ue.division as division', */
            'u.isActive as active',
            'u.code',
            'c.locale',
            'ue.gender as gender',
            'uc.points',
            'uc.startedAt',
            'uc.finishedAt',
            '(' . $subquery . ') as timeSpent',
            'CONCAT(ac.firstName, \' \', ac.lastName) AS announcementCreator',
        ])
            ->leftJoin('uc.announcement', 'a')
            ->leftJoin('a.createdBy', 'ac')
            ->leftJoin('uc.user', 'u')
            ->leftJoin('u.extra', 'ue')
            ->leftJoin('ue.category', 'pc')
            ->leftJoin('ue.department', 'd')
            ->leftJoin('ue.center', 'center')
            ->join('uc.course', 'c')
            ->where('uc.course = :courseID or c.translation = :courseID')
            ->setParameter('courseID', $conditions['courseID'])
            ->orderBy('uc.startedAt', 'ASC');

        if (!empty($conditions['dateFrom'])) {
            if ($starDateTime = \DateTime::createFromFormat('Y-m-d H:i', $conditions['dateFrom'] . ' 00:00')) {
                $query->andWhere($query->expr()->orX('uc.startedAt >= :start', 'uc.finishedAt >= :start'))
                    ->setParameter('start', $starDateTime);
            }

            // course must be started after start date
            if ($conditions['courseStartedIntime'] && 1 == $conditions['courseStartedIntime']) {
                $query->andWhere('uc.startedAt >= :start');
            }
            // course must be finished after start date
            if ($conditions['courseFinishedIntime'] && 1 == $conditions['courseFinishedIntime']) {
                $query->andWhere('uc.finishedAt >= :start');
            }
        }

        if (!empty($conditions['dateTo'])) {
            if ($endDateTime = \DateTime::createFromFormat('Y-m-d H:i', $conditions['dateTo'] . ' 23:59')) {
                $query->andWhere($query->expr()->orX('uc.startedAt <= :end', 'uc.finishedAt <= :end'))
                    ->setParameter('end', $endDateTime);
            }

            // course must be started before end date
            if (\array_key_exists('courseStartedIntime', $conditions) && !empty($conditions['courseStartedIntime'])) {
                if (1 == $conditions['courseStartedIntime']) {
                    $query->andWhere('uc.startedAt <= :end');
                }
            }
            // course must be finished before end date
            if (\array_key_exists('courseFinishedIntime', $conditions) && !empty($conditions['courseFinishedIntime'])) {
                if (1 == $conditions['courseFinishedIntime']) {
                    $query->andWhere('uc.finishedAt <= :end');
                }
            }
        }

        if (\array_key_exists('courseFinishedIntime', $conditions) && !empty($conditions['courseFinishedIntime'])) {
            if (1 == $conditions['courseFinishedIntime']) {
                $query->andWhere('uc.finishedAt IS NOT NULL'); // course must be finished completely
            }
        }

        if (!empty($conditions['customFilters'])) {
            // $query->leftJoin('c.filters', 'f');
            $filtersId = explode(',', $conditions['customFilters']);
            $filtersArray = [];

            foreach ($filtersId as $filterValue) {
                $filter = $this->_em->getRepository(Filter::class)->find($filterValue);
                $filtersArray[$filter->getFilterCategory()->getId()][] = $filterValue;
                /* $query->andWhere('f.id = :filterValue')
                    ->setParameter('filterValue', $filterValue); */
            }
        } elseif (\in_array('ROLE_MANAGER', $user->getRoles())) {
            // Si el usuario que solicitó la exportación es Manager, habrá que filtrar por los filtros que el maneja
            // Sacamos $user de ese id
            $filters = $user->getFilters();
            $filtersArray = [];
            foreach ($filters as $filter) {
                $filtersArray[$filter->getFilterCategory()->getId()][] = $filter->getId();
            }
        }

        if (isset($filtersArray)) {
            foreach ($filtersArray as $category_id => $filters) {
                $query->innerJoin('u.filter', 'f' . $category_id);
                $query->andWhere($query->expr()->in('f' . $category_id . '.id', $filters));
            }
        }

        return $query->getQuery()->getResult();
        // return $query->getQuery()->getSQL();
    }

    public function getAllUsersWithAtLeastOneCourseFinished(array $conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(Distinct(uc.user))')
            ->andWhere('uc.finishedAt IS NOT NULL');

        $query->leftJoin('uc.user', 'u');
        $query->leftJoin('u.extra', 'ue');

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.finishedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.finishedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        $this->setQueryFilters($query, $conditions);

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function getTrainedUsersByMaxDate(\DateTime $dateTime)
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(distinct(uc.user))')
            ->where('uc.finishedAt < :date')
            ->setParameter('date', $dateTime->format('Y-m-d'));

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function getStartedCoursesByMaxDate(\DateTime $dateTime)
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id)')
            ->where('uc.startedAt < :date')
            ->setParameter('date', $dateTime->format('Y-m-d'));

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function getFinishedCoursesByMaxDate(\DateTime $dateTime)
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id)')
            ->where('uc.finishedAt < :date')
            ->setParameter('date', $dateTime->format('Y-m-d'));

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function getAcumulativeUsersFormed($conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(distinct u.id) as total')
            ->leftJoin('uc.user', 'u')
            ->andWhere('uc.finishedAt is not null');

        /*   ->andWhere('uc.startedAt <= :lastDay')
            ->setParameter('lastDay', $lastDayOfMonth)  */

        $this->setUserSearchFilters($query, 'u', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.finishedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.finishedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getResult();
    }

    public function getTotalByStructure($conditions = [])
    {
        $query = $this->_em->createQueryBuilder()
            ->select('f.name,
                    count(distinct(uf.id)) as count')
            ->from(Filter::class, 'f')
            ->leftJoin('f.users', 'uf')
            ->leftJoin('uf.courses', 'uc')
            ->andWhere('uc.finishedAt IS NOT NULL')
            ->andWhere('f.filterCategory = 6')
            ->groupBy('f.id');

        if (!empty($conditions['dateFrom'])) {
            $query
                ->andWhere('uc.finishedAt >= :dateFrom')
                ->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query
                ->andWhere('uc.finishedAt <= :dateTo')
                ->setParameter('dateTo', $conditions['dateTo']);
        }

        $this->setUserSearchFilters($query, 'uf', $conditions);

        return $query->getQuery()->getResult();
    }

    private function filterDateString($conditions, $prefix = '', $varName = 'created_at'): string
    {
        $string = '';
        if (!empty($conditions['dateFrom'])) {
            $string = " AND {$prefix}{$varName} >= '{$conditions['dateFrom']}' ";
        }
        if (!empty($conditions['dateTo'])) {
            $string .= " AND {$prefix}{$varName} <= '{$conditions['dateTo']}' ";
        }

        return $string;
    }

    private function filterConditionStringInFilter($conditions, $prefixUserTable = 'uc'): string
    {
        $string = '';

        if (isset($conditions['country'])) {
            $string .= " AND eu.country = '{$conditions['country']}' ";
        }

        if (isset($conditions['center'])) {
            $string .= " AND eu.center_id = '{$conditions['center']}' ";
        }

        if (!empty($conditions['category'])) {
            $string .= " AND eu.category_id = '{$conditions['category']}' ";
        }

        if (!empty($conditions['departament'])) {
            $string .= " AND eu.departament_id = '{$conditions['departament']}' ";
        }

        if (!empty($conditions['gender'])) {
            $string .= " AND eu.gender = '{$conditions['gender']}' ";
        }

        if (!empty($conditions['division'])) {
            $string .= " AND eu.division = '{$conditions['division']}' ";
        }
        if (!empty($conditions['filters'])) {
            foreach ($conditions['filters'] as $filters) {
                $valueFilter = implode(',', $filters);
                // $string .= " AND uf.filter_id = '{$valueFilter}' ";
                $string .= ' AND ' . $prefixUserTable . ".user_id in (select a.user_id from user_filter a where a.filter_id = '{$valueFilter}')";
            }
        }

        return $string;
    }

    private function filterConditionString($conditions): string
    {
        $string = '';

        if (isset($conditions['active'])) {
            $string .= " AND u.is_active = '{$conditions['active']}' ";
        }

        if (isset($conditions['country'])) {
            $string .= " AND eu.country = '{$conditions['country']}' ";
        }

        if (isset($conditions['center'])) {
            $string .= " AND eu.center_id = '{$conditions['center']}' ";
        }

        if (!empty($conditions['category'])) {
            $string .= " AND eu.category_id = '{$conditions['category']}' ";
        }

        if (!empty($conditions['departament'])) {
            $string .= " AND eu.departament_id = '{$conditions['departament']}' ";
        }

        if (!empty($conditions['gender'])) {
            $string .= " AND eu.gender = '{$conditions['gender']}' ";
        }

        if (!empty($conditions['division'])) {
            $string .= " AND eu.division = '{$conditions['division']}' ";
        }

        if (!empty($conditions['filters'])) {
            foreach ($conditions['filters'] as $filters) {
                $valueFilter = implode(',', $filters);
                $string .= " AND uf.filter_id = '{$valueFilter}' ";
            }
        }

        return $string;
    }

    private function filterJoinString($conditions, $tableAlias = 'uf'): string
    {
        $string = '';

        if (!empty($conditions['filters'])) {
            foreach ($conditions['filters'] as $category_id => $filters) {
                $string .= " join user_filter f$category_id on f$category_id.user_id = $tableAlias.user_id and f$category_id.filter_id in (" . implode($filters) . ') ';
            }
        }

        if (isset($conditions['active'])) {
            $string .= " join user u on u.id = $tableAlias.user_id ";
        }

        return $string;
    }

    public function getStructureOrDepartmentGeneral($conditions = [], $category)
    {
        $dateFilter = $this->filterDateString($conditions, '', 'finished_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                distinct f.name, f.id
                FROM filter f
                JOIN user_filter uf on f.id = uf.filter_id
                JOIN user_extra eu on eu.user_id = uf.user_id
                JOIN (SELECT distinct(user_id) user_id FROM user_course
                    WHERE finished_at is not null ' . $dateFilter . ') u on u.user_id = uf.user_id
                JOIN user_filter estructura on estructura.user_id = uf.user_id
                    and estructura.filter_id = ' . $category . '
                ' . $generalJoin . '
                WHERE filter_category_id = 4 ' . $generalFilter;

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getStructureOrDepartmentDetail($conditions = [], $category, $colectivo, $id)
    {
        $dateFilter = $this->filterDateString($conditions, '', 'finished_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                distinct count(f.id) as total
                FROM filter f
                JOIN user_filter uf on f.id = uf.filter_id
                JOIN (SELECT distinct(user_id) user_id FROM user_course
                    WHERE finished_at is not null ' . $dateFilter . ') u on u.user_id = uf.user_id
                JOIN user_filter estructura on estructura.user_id = uf.user_id
                    and estructura.filter_id = ' . $category . "
                join user_filter division on division.user_id = uf.user_id
                join filter filter_division on filter_division.id = division.filter_id and filter_division.name = '" . $colectivo . "'
                $generalJoin
                WHERE f.filter_category_id = 4 and f.id = " . $id . ' ' . $generalFilter;

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getStructureOrDepartment($conditions = [], $category)
    {
        $dateFilter = $this->filterDateString($conditions, '', 'finished_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                f.name, f.id, f.user_id
                FROM filter f
                JOIN user_filter uf on f.id = uf.filter_id
                JOIN (SELECT distinct(user_id) user_id FROM user_course
                    WHERE finished_at is not null ' . $dateFilter . ') u on u.user_id = uf.user_id
                JOIN user_filter estructura on estructura.user_id = uf.user_id
                    and estructura.filter_id = ' . $category . '
                ' . $generalJoin . '
                WHERE filter_category_id = 4 ' . $generalFilter . '
                GROUP BY f.id ORDER BY f.name ASC';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function checkSql($sql)
    {
        if (str_contains($sql, 'eu') && !str_contains($sql, 'user_extra')) {
            $sql = str_replace('WHERE', 'JOIN user_extra eu on eu.user_id =  WHERE', $sql);
        }

        return $sql;
    }

    public function getSegmentedCountryOrDivision($conditions = [], $category)
    {
        $dateFilter = $this->filterDateString($conditions, '', 'finished_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                f.name,
                count(uf.user_id) as total
                FROM filter f
                JOIN user_filter uf on f.id = uf.filter_id
                JOIN user_extra eu on eu.user_id = uf.user_id
                JOIN (SELECT distinct(user_id) user_id FROM user_course WHERE finished_at is not null  ' . $dateFilter . ') u on u.user_id = uf.user_id
                ' . $generalJoin . '
                WHERE filter_category_id = ' . $category . ' ' . $generalFilter . '
                GROUP BY f.id';
        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedAndGroupBy($conditions = [])
    {
        $dateFilterFinish = $this->filterDateString($conditions, '', 'uc.finished_at');
        $dateFilterValue = $this->filterDateString($conditions, '', 'uc.valued_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                    count(uc.id) as total
                FROM filter f
                        JOIN user_filter uf on f.id = uf.filter_id
                        JOIN user_extra eu on eu.user_id = uf.user_id
                        JOIN user_course uc on uc.user_id = uf.user_id ' . $dateFilterFinish . '
                        JOIN user_filter estructura on estructura.user_id = uf.user_id and estructura.filter_id = 1
                        ' . $generalJoin . '
                WHERE filter_category_id = 4 and uc.finished_at is not null ' . $generalFilter . '
                UNION ALL
                SELECT
                    count(uc.id) as total
                FROM filter f
                    JOIN user_filter uf on f.id = uf.filter_id
                    JOIN user_extra eu on eu.user_id = uf.user_id
                    JOIN user_course uc on uc.user_id = uf.user_id ' . $dateFilterValue . '
                    JOIN user_filter estructura on estructura.user_id = uf.user_id and estructura.filter_id = 1
                    ' . $generalJoin . '
                WHERE filter_category_id = 4 and uc.valued_at is not null ' . $generalFilter;

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getAcumulativeCourse($conditions = [], $monthDate, $yearDate)
    {
        $lastDayOfMonth = date('Y-m-t', strtotime("$yearDate-$monthDate-1"));

        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as total')

            //            ->andWhere('month(uc.finishedAt) = :month')
            //            ->setParameter('month', $monthDate)
            //            ->andWhere('year(uc.finishedAt) = :year')
            //            ->setParameter('year', $yearDate)
            ->andWhere('uc.finishedAt <= :lastDay')
            ->setParameter('lastDay', $lastDayOfMonth);

        if (!\is_null($monthDate)) {
            $query = $query->andWhere('uc.startedAt <= :lastDay')
                ->setParameter('lastDay', $lastDayOfMonth);
        }

        $this->setFinishedCoursesFilters($query, $conditions);

        return $query->getQuery()->getResult();
    }

    public function getAcumulativeCourseStarted($conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as total');

        /*   if (!is_null($monthDate)) {
            $query = $query->andWhere('uc.startedAt <= :lastDay')
                ->setParameter('lastDay', $lastDayOfMonth);
        } */

        $this->setSearchFilters($query, 'uc', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.startedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.startedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getResult();
    }

    public function getAcumulativeCourseInProccesss($conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as total');
        //  ->andWhere('uc.finishedAt is null');

        $this->setSearchFilters($query, 'uc', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.startedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.startedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getResult();
    }

    public function getAcumulativeCourseFinished($conditions = [], $monthDate = null, $yearDate = null)
    {
        $lastDayOfMonth = date('Y-m-t', strtotime("$yearDate-$monthDate-1"));

        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as total')
            ->andWhere('uc.finishedAt is not null');

        if (!\is_null($monthDate)) {
            $query = $query->andWhere('uc.finishedAt <= :lastDay')
                ->setParameter('lastDay', $lastDayOfMonth);
        }

        $this->setFinishedCoursesFilters($query, $conditions);

        return $query->getQuery()->getResult();
    }

    public function getAcumulativeCourseValued($conditions = [], $monthDate = null, $yearDate = null)
    {
        $lastDayOfMonth = date('Y-m-t', strtotime("$yearDate-$monthDate-1"));

        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as total')
            ->andWhere('uc.finishedAt is not null')

            //            ->andWhere('month(uc.valuedAt) = :month')
            //            ->setParameter('month', $monthDate)
            //            ->andWhere('year(uc.valuedAt) = :year')
            //            ->setParameter('year', $yearDate)
        ;

        if (!\is_null($monthDate)) {
            $query = $query->andWhere('uc.finishedAt <= :lastDay')
                ->setParameter('lastDay', $lastDayOfMonth);
        }

        $this->setFinishedCoursesFilters($query, $conditions);

        return $query->getQuery()->getResult();
    }

    public function getSegmentedCourseCountry($conditions = [], $type)
    {
        if (1 === $type) {
            $dateName = 'uc.finished_at';
        } else {
            $dateName = 'uc.valued_at';
        }
        $dateFilter = $this->filterDateString($conditions, '', $dateName);
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                f.name,
                count(uc.id) as total
            FROM filter f
                    JOIN user_filter uf on f.id = uf.filter_id
                    JOIN user_extra eu on eu.user_id = uf.user_id
                    JOIN user_course uc on uc.user_id = uf.user_id ' . $dateFilter . '
                    ' . $generalJoin . '
            WHERE filter_category_id = 2 AND ' . $dateName . ' is not null ' . $generalFilter . '
            GROUP BY f.id
            ORDER BY f.name asc';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedCourseCountryTotal($conditions = [], $type)
    {
        if (1 === $type) {
            $dateName = 'uc.finished_at';
        } else {
            $dateName = 'uc.valued_at';
        }
        $dateFilter = $this->filterDateString($conditions, '', $dateName);
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                count(uc.id) as total
            FROM filter f
                    JOIN user_filter uf on f.id = uf.filter_id
                    JOIN user_extra eu on eu.user_id = uf.user_id
                    JOIN user_course uc on uc.user_id = uf.user_id ' . $dateFilter . '
                    ' . $generalJoin . '
            WHERE filter_category_id = 2 AND ' . $dateName . ' is not null ' . $generalFilter . '
            ORDER BY f.name asc';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedCourseGroupByStructureHotel($conditions = [], $type, $dateType)
    {
        if ('F' === $type) {
            $dateName = 'uc.finished_at';
        } elseif ('V' === $type) {
            $dateName = 'uc.valued_at';
        }
        $dateFilter = $this->filterDateString($conditions, '', $dateName);
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                f.name,
                count(uc.id) as total
            FROM filter f
                    JOIN user_filter uf on f.id = uf.filter_id
                    JOIN user_extra eu on eu.user_id = uf.user_id
                    JOIN user_course uc on uc.user_id = uf.user_id
                        and ' . $dateName . ' is not null ' . $dateFilter . '
                    JOIN user_filter estructura on estructura.user_id = uf.user_id and estructura.filter_id = ' . $dateType . '
                    ' . $generalJoin . '
            WHERE filter_category_id = 4 ' . $generalFilter . '
            GROUP BY f.id
            ORDER BY f.name asc';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getAcumulativeHour($conditions, $monthDate, $yearDate)
    {
        $lastDayOfMonth = date('Y-m-t', strtotime("$yearDate-$monthDate-1"));

        $dateFilterFrom = '';
        if (!empty($conditions['dateFrom'])) {
            $dateFilterFrom = " AND ucc.finished_at >= '" . $conditions['dateFrom'] . "'";
        }

        $dateFilterTo = '';
        if (!empty($conditions['dateTo'])) {
            $dateFilterTo = " AND ucc.finished_at <= '" . $conditions['dateTo'] . "'";
        }
        $active = '';
        if (isset($conditions['active'])) {
            $active .= " AND u.is_active = '{$conditions['active']}' ";
        }

        $filters = ['division', 'country', 'center', 'department', 'category', 'collective'];

        $filtersCondition = [];

        foreach ($filters as $filter) {
            if (!empty($conditions[$filter])) {
                $filtersCondition['filters'][] = $conditions[$filter];
            }
        }

        $dateFilterStart = $this->filterDateString($conditions, '', 'ucc.started_at');
        //        $dateFilterFinish = $this->filterDateString($conditions, '', 'ucc.finished_at');
        $generalFilter = $this->filterConditionStringInFilter($filtersCondition);

        //        $dateFilterTotal = '';
        //        if(!empty($dateFilterStart)) {
        //            $dateFilterTotal = ' AND (('.substr($dateFilterStart, 5).') OR ('.substr($dateFilterFinish, 5).'))';
        //        }

        $sql = "SELECT
                    sum(ucc.time_spent) / 3600 as horas,
                    (sum(ucc.time_spent) / 3600) / count(DISTINCT (uc.user_id)) as promedio
                FROM user_course_chapter ucc
                LEFT JOIN user_course uc on uc.id = ucc.user_course_id
                LEFT JOIN user_extra eu on eu.user_id = uc.user_id
                LEFT JOIN user u on uc.user_id  = u.id
                WHERE
                    ucc.started_at <= '" . $lastDayOfMonth . " 23:59:59'
                    " . $dateFilterFrom . '
                    ' . $dateFilterTo . '
                    ' . $active . '
                    ' . $generalFilter;

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedHourTotal($conditions = [])
    {
        $dateFilterStart = $this->filterDateString($conditions, '', 'ucc.started_at');
        $dateFilterFinish = $this->filterDateString($conditions, '', 'ucc.finished_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions, 'uc');

        $dateFilterTotal = '';
        if (!empty($dateFilterStart)) {
            $dateFilterTotal = ' AND ((' . substr($dateFilterStart, 5) . ') OR (' . substr($dateFilterFinish, 5) . '))';
        }

        $sql = 'SELECT
                    sum(ucc.time_spent) / 3600 as horas,
                    (sum(ucc.time_spent) / 3600) / count(DISTINCT (uc.user_id)) as promedio
                FROM user_course_chapter ucc
                JOIN user_course uc on uc.id = ucc.user_course_id
                ' . $generalJoin . '
                WHERE
                    1=1 ' . $dateFilterTotal . '
                        ' . $generalFilter;

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedGroupStructureHotel($conditions = [], $type)
    {
        $dateFilterStart = $this->filterDateString($conditions, '', 'ucc.started_at');
        $dateFilterFinish = $this->filterDateString($conditions, '', 'ucc.finished_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $dateFilterTotal = '';
        if (!empty($dateFilterStart)) {
            $dateFilterTotal = ' AND ((' . substr($dateFilterStart, 5) . ') OR (' . substr($dateFilterFinish, 5) . '))';
        }

        $sql = 'SELECT
                    f.name,
                    sum(ucc.time_spent) / 3600 as horas,
                    (sum(ucc.time_spent) / 3600) / count(distinct (uc.user_id)) as promedio
                FROM
                    user_course_chapter ucc
                JOIN user_course uc on uc.id = ucc.user_course_id
                JOIN user_filter uf on uc.user_id = uf.user_id
                JOIN user_extra eu on eu.user_id = uf.user_id
                JOIN filter f on uf.filter_id = f.id and f.filter_category_id = 4
                JOIN user_filter estructura on estructura.user_id = uf.user_id
                    and estructura.filter_id = ' . $type . '
                ' . $generalJoin . '
                WHERE
                        1=1 ' . $dateFilterTotal . '
                        ' . $generalFilter . '
                GROUP BY f.id
                ORDER BY f.name asc';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedHourCountry($conditions = [])
    {
        $dateFilterStart = $this->filterDateString($conditions, '', 'ucc.started_at');
        $dateFilterFinish = $this->filterDateString($conditions, '', 'ucc.finished_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $dateFilterTotal = '';
        if (!empty($dateFilterStart)) {
            $dateFilterTotal = ' AND ((' . substr($dateFilterStart, 5) . ') OR (' . substr($dateFilterFinish, 5) . '))';
        }

        $sql = 'SELECT
                    f.name,
                    sum(ucc.time_spent) / 3600 as horas,
                    (sum(ucc.time_spent) / 3600) / count(distinct (uc.user_id)) as promedio
                FROM
                    user_course_chapter ucc
                        JOIN user_course uc on uc.id = ucc.user_course_id
                        JOIN user_filter uf on uc.user_id = uf.user_id
                        JOIN user_extra eu on eu.user_id = uf.user_id
                        JOIN filter f on uf.filter_id = f.id and f.filter_category_id = 2
                ' . $generalJoin . '
                WHERE
                        1=1 ' . $dateFilterTotal . '
                        ' . $generalFilter . '
                GROUP BY f.id
                ORDER BY f.name asc';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedHourSchool($conditions = [])
    {
        $dateFilterStart = $this->filterDateString($conditions, '', 'ucc.started_at');
        $dateFilterFinish = $this->filterDateString($conditions, '', 'ucc.finished_at');
        $generalFilter = $this->filterConditionStringInFilter($conditions);
        $generalJoin = $this->filterJoinString($conditions, 'uc');

        $dateFilterTotal = '';
        if (!empty($dateFilterStart)) {
            $dateFilterTotal = ' AND ((' . substr($dateFilterStart, 5) . ') OR (' . substr($dateFilterFinish, 5) . '))';
        }

        return $this->getEntityManager()->getConnection()->prepare(
            '
        	SELECT
                cc.name,
            	sum(ucc.time_spent) / 3600 as horas,
                (sum(ucc.time_spent) / 3600) / count(distinct (uc.user_id)) as promedio
            FROM user_course_chapter ucc
                JOIN user_course uc on uc.id = ucc.user_course_id
                JOIN user_extra eu on eu.user_id = uc.user_id
                JOIN course c on uc.course_id = c.id
                JOIN course_category cc on c.category_id = cc.id
                ' . $generalJoin . '
            WHERE
                1=1 ' . $dateFilterTotal . '
                ' . $generalFilter . '
            GROUP BY cc.id
            ORDER BY cc.name asc'
        )->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedHourStructureVsHotel($conditions = [], $idFilter)
    {
        $dateFilterStart = $this->filterDateString($conditions, '', 'ucc.started_at');
        $dateFilterFinish = $this->filterDateString($conditions, '', 'ucc.finished_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $dateFilterTotal = '';
        if (!empty($dateFilterStart)) {
            $dateFilterTotal = ' AND ((' . substr($dateFilterStart, 5) . ') OR (' . substr($dateFilterFinish, 5) . '))';
        }

        $sql = 'SELECT
                    f.name,
                    sum(ucc.time_spent) / 3600 as horas,
                    (sum(ucc.time_spent) / 3600) / count(distinct (uc.user_id)) as promedio
                FROM
                    user_course_chapter ucc
                        JOIN user_course uc on uc.id = ucc.user_course_id
                        JOIN user_filter uf on uc.user_id = uf.user_id
                        LEFT JOIN user_extra eu on eu.user_id = uf.user_id
                        JOIN filter f on uf.filter_id = f.id and f.filter_category_id = ' . $idFilter . '
                        ' . $generalJoin . '
                WHERE
                        1=1 ' . $dateFilterTotal . '
                        ' . $generalFilter . '
                GROUP BY f.id
                ORDER BY f.name asc';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getAcumulativeAccess($conditions = [], $monthDate, $yearDate)
    {
        $dateFilter = $this->filterDateString($conditions, '', 'ul.created_at');
        //        $generalFilter = $this->filterConditionStringInFilter($conditions, 'ul');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions, 'ul');

        $lastDayOfMonth = date('Y-m-t', strtotime("$yearDate-$monthDate-1"));

        $sql = "SELECT
                    count(ul.id) as total,
                    count(distinct (ul.user_id)) as unicos
                FROM user_login ul
                JOIN user_extra eu on eu.user_id = ul.user_id
                $generalJoin
                WHERE ul.created_at is not null
                 and ul.created_at <= '$lastDayOfMonth'
                 " . $dateFilter . ' ' . $generalFilter;

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedAccessTotal($conditions = [])
    {
        $dateFilter = $this->filterDateString($conditions, '', 'ul.created_at');
        //        $generalFilter = $this->filterConditionStringInFilter($conditions, 'ul');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions, 'ul');

        $sql = 'SELECT
                    count(ul.id) as total,
                    count(distinct (ul.user_id)) as unicos
                FROM user_login ul
                JOIN user_extra eu on eu.user_id = ul.user_id
                ' . $generalJoin . '
                WHERE ul.created_at is not null ' . $dateFilter . ' ' . $generalFilter;

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedAccessGroupBy($conditions = [], $type)
    {
        $dateFilter = $this->filterDateString($conditions, '', 'user_login.created_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                    f.name,
                    count(user_login.id) as total,
                    count(distinct (user_login.user_id)) as unicos
                FROM user_login
                JOIN user_filter uf on user_login.user_id = uf.user_id
                JOIN user_extra eu on eu.user_id = uf.user_id
                JOIN filter f on uf.filter_id = f.id
                    and f.filter_category_id = ' . $type . '
                ' . $generalJoin . '
                WHERE user_login.created_at is not null ' . $dateFilter . ' ' . $generalFilter . '
                GROUP BY f.id
                ORDER BY f.name asc';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getSegmentedAccessGroupByStructureHotel($conditions = [], $type)
    {
        $dateFilter = $this->filterDateString($conditions, '', 'user_login.created_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                    f.name,
                    count(user_login.id) as total,
                    count(distinct (user_login.user_id)) as unicos
                FROM user_login
                        JOIN user_filter uf on user_login.user_id = uf.user_id
                        JOIN user_extra eu on eu.user_id = uf.user_id
                        JOIN filter f on uf.filter_id = f.id and f.filter_category_id = 4
                        JOIN user_filter estructura on estructura.user_id = uf.user_id
                            and estructura.filter_id = ' . $type . '
                        ' . $generalJoin . '
                WHERE user_login.created_at IS NOT NULL ' . $dateFilter . ' ' . $generalFilter . '
                GROUP BY f.id
                ORDER BY f.name asc';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getUserCourses($user)
    {
        $query = $this->createQueryBuilder('uc')
            ->join('uc.course', 'c')
            ->andWhere('uc.user = :user')
            ->andWhere('c.deletedAt IS NULL')
            ->setParameter('user', $user);

        return $query->getQuery()
            ->getResult();
    }

    public function getNpsAnnouncent($announcement)
    {
        $query = $this->createQueryBuilder('uc')
            ->leftJoin('uc.user', 'u')
            ->andWhere('uc.announcement = :announcement')
            ->setParameter('announcement', $announcement);

        return $query->getQuery()->getResult();
    }

    public function topCourse()
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(c.id) as count, c.name')
            ->leftJoin(
                Course::class,
                'c',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'c.id = uc.course'
            )
            ->andWhere('uc.finishedAt is not null')
            ->groupBy('c.id');

        $query->addOrderBy('count(c.id)', 'DESC');

        return $query
            ->setMaxResults(10)
            ->getQuery()
            ->getArrayResult();
    }

    public function getGroupCourseByPersons($type, array $conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as total')
            ->andWhere('uc.finishedAt IS NOT NULL');

        $this->setSearchFilters($query, 'uc', $conditions);

        $query->groupBy('uc.user');

        if ('poco' === $type) {
            $query->having('count(uc.id) <= 1');
        } elseif ('medio' === $type) {
            $query->having('count(uc.id) >= 2 and count(uc.id) <= 5');
        } elseif ('bastante' === $type) {
            $query->having('count(uc.id) >= 6 and count(uc.id) <= 9');
        } elseif ('muy' === $type) {
            $query->having('count(uc.id) >= 10');
        }

        return $query->getQuery()->getResult();
    }

    public function getSegmentedHourGeneral($conditions = [])
    {
        $noDateRange = true;
        $data = [];

        $query = $this->_em->createQueryBuilder()
            ->select('sum(ucc.timeSpent) / 3600 as horas,
                    (sum(ucc.timeSpent) / 3600) / count(distinct (uc.user)) as promedio')
            ->from(UserCourseChapter::class, 'ucc')
            ->leftJoin('ucc.userCourse', 'uc')
            ->andWhere('ucc.finishedAt IS NOT NULL'); // BY DEFAULT CHECK FOR HOURS FROM FINISHED CHAPTERS

        if (!empty($conditions['dateFrom'])) {
            $noDateRange = false;
            $query
                ->andWhere('ucc.finishedAt >= :dateFrom')
                ->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $noDateRange = false;
            $query
                ->andWhere('ucc.finishedAt <= :dateTo')
                ->setParameter('dateTo', $conditions['dateTo']);
        }

        // WHEN THERE IS NO DATE FILTER WE NEED TO RETRIEVE ALL HOURS, BOTH FROM FINISHED AND IN PROCESS TO CURRENT DATE
        if (true === $noDateRange) {
            $subquery = $this->_em->createQueryBuilder()
                ->select('sum(ucc.timeSpent) / 3600 as rawhoras,
                    (sum(ucc.timeSpent) / 3600) / count(distinct (uc.user)) as rawpromedio')
                ->from(UserCourseChapter::class, 'ucc')
                ->leftJoin('ucc.userCourse', 'uc');

            $data['subresult'] = $subquery->getQuery()->getResult();
        } else {
            $data['subresult'] = [];
        }

        $this->setSearchFilters($query, 'uc', $conditions);

        $data['query'] = $query->getQuery()->getSQL();
        $data['result'] = $query->getQuery()->getResult();
        $data['result'] = array_merge($data['result'], $data['subresult']);

        return $data;
    }

    public function getTotalPersonInCourse($conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(distinct u.id) as total')
            ->leftJoin('uc.user', 'u');

        $this->setUserSearchFilters($query, 'u', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.startedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.startedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getResult();
    }

    public function getTotalPersonGeneral($conditions = [])
    {
        $dateFilter = $this->filterDateString($conditions, '', 'finished_at');
        $generalFilter = $this->filterConditionString($conditions);
        $generalJoin = $this->filterJoinString($conditions);

        $sql = 'SELECT
                count(uf.user_id) as total
                FROM filter f
                JOIN user_filter uf on f.id = uf.filter_id
                JOIN user u on u. = uf.user_id
                ' . $generalJoin . '
                WHERE 1=1 ' . $generalFilter;
        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getTotalPersonWithAtLeast1Course($conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(distinct u.id) as total')
            ->leftJoin('uc.user', 'u')
            ->andWhere('uc.finishedAt is not null')
            ->andWhere('u.deletedAt is null');

        $this->setUserSearchFilters($query, 'u', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.finishedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.finishedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getResult();
    }

    public function getCourseStartedOrFinished(string $type, array $conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('c.id, c.name, count(c.id) as count')
            ->leftJoin('uc.course', 'c')
            ->groupBy('c.id');
        $query->andWhere('uc.finishedAt is ' . ('finished' == $type ? 'not' : '') . ' null');

        $this->setSearchFilters($query, 'uc', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.finishedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.finishedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getResult();
    }

    public function getCountCoursesStarted(array $conditions = []): int
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as count');
        $this->setSearchFilters($query, 'uc', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query
                ->andWhere('uc.startedAt >= :dateFrom')
                ->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query
                ->andWhere('uc.startedAt <= :dateTo')
                ->setParameter('dateTo', $conditions['dateTo']);
        }

        try {
            return (int) $query->getQuery()->getSingleScalarResult();
        } catch (NoResultException|NonUniqueResultException $e) {
            return 0;
        }
    }

    public function getCountCoursesInProcess(array $conditions = []): int
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as count')
            ->andWhere('uc.finishedAt is null');
        $this->setSearchFilters($query, 'uc', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.startedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.startedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        try {
            return (int) $query->getQuery()->getSingleScalarResult();
        } catch (NoResultException|NonUniqueResultException $e) {
            return 0;
        }
    }

    public function getCountCoursesFinished(array $conditions = []): int
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as count')
            ->andWhere('uc.finishedAt is not null');
        $this->setSearchFilters($query, 'uc', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query
                ->andWhere('uc.finishedAt >= :dateFrom')
                ->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query
                ->andWhere('uc.finishedAt <= :dateTo')
                ->setParameter('dateTo', $conditions['dateTo']);
        }

        return (int) $query->getQuery()->getSingleScalarResult();
    }

    public function getCountUserWhitCoursesFinished(array $conditions = []): int
    {
        $qb = $this->createQueryBuilder('uc')
            ->select('COUNT(DISTINCT uc.user) as count')
            ->where('uc.finishedAt IS NOT NULL');

        $this->setSearchFilters($qb, 'uc', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $qb->andWhere('uc.startedAt >= :dateFrom')
                ->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $qb->andWhere('uc.startedAt <= :dateTo')
                ->setParameter('dateTo', $conditions['dateTo']);
        }

        $subquery = $this->_em->createQueryBuilder()
            ->select('1')
            ->from('App\Entity\UserCourse', 'uc_ant')
            ->where('uc_ant.user = uc.user');

        if (!empty($conditions['dateFrom'])) {
            $subquery->andWhere('uc_ant.startedAt < :dateFrom')
                ->setParameter('dateFrom', $conditions['dateFrom']);
        }

        $qb->andWhere($qb->expr()->not($qb->expr()->exists($subquery->getDQL())));

        return (int) $qb->getQuery()->getSingleScalarResult();
    }

    public function getCourseActivityHeatMap($field = 'started', $conditions = []): array
    {
        $field = \in_array($field, ['finish', 'finished']) ? 'finishedAt' : 'startedAt';

        $data = [];
        for ($week = 0; $week <= 6; ++$week) {
            for ($hour = 0; $hour <= 22; $hour += 2) {
                $time = $hour / 2;
                $data[$week . '_' . $time] = [
                    'weekday' => $week,
                    'time' => $time,
                    'count' => 0,
                ];
            }
        }

        $query = $this->createQueryBuilder('uc')
            ->select("count(uc.id) as count, weekday(uc.$field ) AS weekday,
                    CASE
                        WHEN HOUR ( uc.$field ) < 2 THEN  0
                        WHEN HOUR ( uc.$field ) < 4 THEN  1
                        WHEN HOUR ( uc.$field ) < 6 THEN  2
                        WHEN HOUR ( uc.$field ) < 8 THEN  3
                        WHEN HOUR ( uc.$field ) < 10 THEN 4
                        WHEN HOUR ( uc.$field ) < 12 THEN 5
                        WHEN HOUR ( uc.$field ) < 14 THEN 6
                        WHEN HOUR ( uc.$field ) < 16 THEN 7
                        WHEN HOUR ( uc.$field ) < 18 THEN 8
                        WHEN HOUR ( uc.$field ) < 20 THEN 9
                        WHEN HOUR ( uc.$field ) < 22 THEN 10
                        ELSE 11
                    END AS time")
            ->andWhere('uc.' . $field . ' IS NOT NULL')
            ->groupBy('weekday, time');

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere("uc.$field >= :dateFrom")->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere("uc.$field <= :dateTo")->setParameter('dateTo', $conditions['dateTo']);
        }

        $query = $this->setSearchFilters($query, 'uc', $conditions);

        $userCourseData = $query->getQuery()->getResult();

        foreach ($userCourseData as $userCourseItem) {
            $data[$userCourseItem['weekday'] . '_' . $userCourseItem['time']]['count'] = (int) $userCourseItem['count'];
        }

        return array_values($data);
    }

    public function countFinishedByUser(string $order = 'DESC', array $conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as count, u.id as userId, u.firstName, u.lastName, u.email')
            ->leftJoin('uc.user', 'u')
            ->andWhere('uc.finishedAt is not null')
            ->groupBy('u.id')
            ->orderBy('count', $order)
            ->setMaxResults(10);

        $this->setUserSearchFilters($query, 'u', $conditions);

        return $query->getQuery()->getResult();
    }

    public function findFinishedUserCourses(User $user, $isOpinionsPlatform)
    {
        $qb = $this->createQueryBuilder('uc')
            ->innerJoin('uc.course', 'c')
            ->where('uc.user = :user');

        if ($isOpinionsPlatform) {
            $qb->andWhere($qb->expr()->isNotNull('uc.finishedAt'));
            $qb->andWhere($qb->expr()->isNotNull('uc.valuedAt'));
        } else {
            $qb->andWhere($qb->expr()->isNotNull('uc.finishedAt'));
        }

        $qb->andWhere($qb->expr()->isNull('uc.announcement'));
        $qb->orderBy('uc.finishedAt', 'ASC');
        $qb->setParameter('user', $user);

        return $qb->getQuery()->getResult();
    }

    public function groupByForExcelReport($content)
    {
        $query = $this->createQueryBuilder('uc')
            ->distinct('u.id, u.firstName, u.lastName, u.email, ue.gender, 0 as timeSpent')
            ->innerJoin(
                User::class,
                'u',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'u.id = uc.user'
            )
            ->innerJoin(
                ItineraryCourse::class,
                'ic',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'ic.course = uc.course'
            )
            ->innerJoin(
                Itinerary::class,
                'i',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'i.id = ic.itinerary'
            )
            ->leftJoin(
                UserExtra::class,
                'ue',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'ue.user = u.id'
            );

        if (!empty($content['tag'])) {
            foreach ($content['tags'] as $key => $item) {
                $param = ':filterTagID' . $key;
                $query
                    ->andWhere('i.tags like ' . $param)
                    ->setParameter($param, '%[' . $item . ']%')
                ;
            }
        }
        if (!empty($content['courseId'])) {
            $query
                ->andWhere('ic.course = :course')
                ->setParameter('course', $content['courseId'])
            ;
        }

        return $query
            ->getQuery()
            // ->setMaxResults(40)
            ->getArrayResult()
        ;
    }

    public function totalTimeByUser($user)
    {
        $query = $this->createQueryBuilder('uc')
            ->select('sum(uc.timeSpent) / 3600 as sumTime')
            ->andWhere('uc.user = :user')
            ->setParameter('user', $user);

        return $query->getQuery()->getResult();
    }

    public function courseTotalUsersFinished(Course $course, bool $findUsers = false, array $usersIds = [], $announcement = null): int
    {
        if ($findUsers && empty($usersIds)) {
            return 0;
        }
        $qb = $this->createQueryBuilder('uc')
            ->select('COUNT(DISTINCT uc.user) as total')
            ->join('uc.course', 'c')
            ->where('uc.course = :course OR c.translation = :course')
            ->andWhere('uc.startedAt IS NOT NULL AND uc.finishedAt IS NOT NULL')
            ->setParameter('course', $course);

        $this->addFilterByAnnouncement($qb, $announcement);

        if (!empty($usersIds)) {
            $qb->join('uc.user', 'u')
                ->andWhere($qb->expr()->in('u.id', $usersIds));
        }

        $result = (int) $qb->getQuery()->getSingleScalarResult();

        return $result;
    }

    public function courseGetTotalUsersStarted(Course $course, bool $findUsers = false, array $usersIds = [], $announcement = null)
    {
        if ($findUsers && empty($usersIds)) {
            return 0;
        }

        $qb = $this->createQueryBuilder('uc')
            ->select('COUNT(DISTINCT(uc.user)) as total')
            ->join('uc.course', 'c')
            ->where('uc.course = :course OR c.translation = :course')
            ->andWhere('uc.startedAt IS NOT NULL AND uc.finishedAt IS NULL');

        $this->addFilterByAnnouncement($qb, $announcement);

        $qb->setParameter('course', $course)
            ->setMaxResults(1);

        if (!empty($usersIds)) {
            $qb->join('uc.user', 'u')
                ->andWhere($qb->expr()->in('u.id', $usersIds));
        }

        try {
            return (int) $qb
                ->getQuery()
                ->getSingleScalarResult();
        } catch (NoResultException|NonUniqueResultException $e) {
            return 0;
        }
    }

    public function userCourseGetData(UserCourseStatsDTO $userCourseStatsDTO)
    {
        $query = $this->createQueryBuilder('uc')
            ->select('uc')
            ->join('uc.course', 'c')
            ->andWhere('uc.user = :user')
            ->andWhere('uc.course = :course OR c.translation = :course');

        $this->addFilterByAnnouncement($query, $userCourseStatsDTO->announcementId);

        $query->setParameter('user', $userCourseStatsDTO->user)
            ->setParameter('course', $userCourseStatsDTO->course);

        if ($userCourseStatsDTO->courseStartedOnTime) {
            $this->applyRangeDateConditions($query, CourseStatsEnum::STARTED_IN_TIME, $userCourseStatsDTO->dateFrom, $userCourseStatsDTO->dateTo);
        }
        if ($userCourseStatsDTO->courseFinishedOnTime) {
            $this->applyRangeDateConditions($query, CourseStatsEnum::FINISHED_IN_TIME, $userCourseStatsDTO->dateFrom, $userCourseStatsDTO->dateTo);
        }

        return $query->setMaxResults(1)->getQuery()->getOneOrNullResult();
    }

    private function applyRangeDateConditions(QueryBuilder $query, int $type = CourseStatsEnum::STARTED_IN_TIME, ?\DateTimeInterface $dateFrom = null, ?\DateTimeInterface $dateTo = null): void
    {
        $column = CourseStatsEnum::STARTED_IN_TIME === $type ? 'startedAt' : 'finishedAt';
        if ($dateFrom) {
            $query->andWhere('uc.' . $column . ' >= :dateFrom')
                ->setParameter('dateFrom', $dateFrom);
        }

        if ($dateTo) {
            $query->andWhere('uc.' . $column . ' <= :dateTo')
                ->setParameter('dateTo', $dateTo);
        }
    }

    public function getUserIdsByDateRangeActivityInCourse(
        Course $course,
        int $type,
        ?\DateTimeInterface $dateFrom = null,
        ?\DateTimeInterface $dateTo = null
    ): array {
        $query = $this->createQueryBuilder('uc')
            ->select('uc')
            ->join('uc.course', 'c')
            ->andWhere('uc.course = :course OR c.translation = :course')
            ->setParameter('course', $course);

        $this->applyRangeDateConditions($query, $type, $dateFrom, $dateTo);

        $result = $query->getQuery()->getResult();

        return array_map(function (UserCourse $userCourse) {
            return $userCourse->getUser()->getId();
        }, $result);
    }

    public function getUserCourseFinished()
    {
        $sql = "
        update user_course as uc, (
                 Select
                    user_course_id,
                    max(ucc_finished) as 'last_finished',
                    sum(chapter_not_finished) as 'chapters_not_finished'
                from (
                Select
                    uc.id as 'user_course_id',
                    uc.user_id,
                    uc.course_id,
                    ch.id as 'chapter_id',
                    ch.title as 'chapter_title',
                    ucc.id as 'ucc_id',
                    ucc.finished_at as 'ucc_finished',
                    if(ucc.finished_at is null, 1, 0) as 'chapter_not_finished'
                from user_course as uc
                left JOIN course c on  uc.course_id = c.id and c.active = 1 and c.deleted_at is null
                left join chapter ch on ch.course_id = c.id and ch.deleted_at is null
                left join user_course_chapter ucc on ucc.user_course_id = uc.id and ucc.chapter_id = ch.id
                where uc.finished_at is null
                order by 2,3,ch.position )
                as aux
                where aux.ucc_finished is not null
                group by aux.user_course_id
                HAVING SUM(chapter_not_finished) = 0
                ) as dts SET
                    uc.finished_at = dts.last_finished
                        where uc.id = dts.user_course_id;
        ";
        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function getUserCourseByUserAndCourse($user, $course, $announcement = null)
    {
        $query = $this->createQueryBuilder('uc')
            ->where('uc.user = :user')
            ->andWhere('uc.course = :course');

        $this->addFilterByAnnouncement($query, $announcement);

        $query->setParameter('user', $user)
            ->setParameter('course', $course);

        return $query->getQuery()
            ->getOneOrNullResult();
    }

    // Filtro de estado de cursos para usuarios
    public function getUserStatusCourseFiltered(?string $status, ?array $userIds, Course $course, $announcement = null)
    {
        $ids = [];
        if ($userIds) {
            foreach ($userIds as $id) {
                $userCourse = $this->findOneBy(['user' => $id, 'course' => $course->getId(), 'announcement' => $announcement]);
                if (UserCourse::STATUS_NO_STARTED == $status && null == $userCourse) {
                    $ids[] = $id;
                } elseif (UserCourse::STATUS_FINISHED == $status && null != $userCourse && (null != $userCourse->getStartedAt() && null != $userCourse->getFinishedAt())) {
                    $ids[] = $id;
                } elseif (UserCourse::STATUS_STARTED == $status && null != $userCourse && (null != $userCourse->getStartedAt() && null == $userCourse->getFinishedAt())) {
                    $ids[] = $id;
                }
            }

            return $ids;
        }

        return [];
    }

    public function findCoursesByUsersDiploma(int $courseId, int $userId, array $conditions)
    {
        $filtersCompletedCourseAndSurvey = $this->settings->get('app.course.filters.diploma');
        $query = $this->createQueryBuilder('uc')
            ->where('uc.user = :userId')
            ->setParameter('userId', $userId);

        if (null != $courseId) {
            $query->andWhere('uc.course = :courseId')
                ->setParameter('courseId', $courseId);
        }

        if ($filtersCompletedCourseAndSurvey) {
            if ('integer' == \gettype($conditions['surveyCompleted'])) {
                if (1 == $conditions['surveyCompleted']) {
                    $query->andWhere('uc.valuedAt IS NOT NULL');
                } else {
                    $query->andWhere('uc.valuedAt IS NULL');
                }
            }

            if ('integer' == \gettype($conditions['courseCompleted'])) {
                if (1 == $conditions['courseCompleted']) {
                    $query->andWhere('uc.finishedAt IS NOT NULL');
                } else {
                    $query->andWhere('uc.finishedAt IS NULL');
                }
            }
        } else {
            $query->andWhere('uc.valuedAt IS NOT NULL')
            ->andWhere('uc.finishedAt IS NOT NULL');
        }

        if (\is_array($conditions['courseFilters']) && !empty($conditions['courseFilters'])) {
            $query->innerJoin('uc.user', 'u');
            $query->innerJoin('u.filter', 'f');
            $filterIds = [];
            foreach ($conditions['courseFilters'] as $filter) {
                $filterIds[] = $filter;
            }
            $query->andWhere($query->expr()->in('f.id', $filterIds));
        }

        return $query->getQuery()->setMaxResults(1)->getOneOrNullResult();
    }

    public function getCourseUserFinished(int $user, Course $course)
    {
        $finished = false;
        $query = $this->createQueryBuilder('uc')
            ->join('uc.course', 'c')
            ->andWhere('uc.user = :user')
            ->andWhere('uc.course = :course')
            ->andWhere('c.deletedAt IS NULL')
            ->andWhere('uc.finishedAt IS NOT NULL')
            ->setParameter('user', $user)
            ->setParameter('course', $course);

        if ($query->getQuery()->setMaxResults(1)->getOneOrNullResult()) {
            $finished = true;
        }

        return $finished;
    }
}
