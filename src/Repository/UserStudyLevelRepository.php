<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\UserStudyLevel;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<UserStudyLevel>
 *
 * @method UserStudyLevel|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserStudyLevel|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserStudyLevel[]    findAll()
 * @method UserStudyLevel[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserStudyLevelRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserStudyLevel::class);
    }

    public function add(UserStudyLevel $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(UserStudyLevel $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findAllOrderByName(): array
    {
        $query = $this->createQueryBuilder('s')
            ->select('s')
            ->orderBy('s.name', 'ASC');

        return $query->getQuery()->getResult();
    }
}
