<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChatChannel;
use App\Entity\ChatServer;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ChatServer>
 *
 * @method ChatServer|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChatServer|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChatServer[]    findAll()
 * @method ChatServer[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChatServerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChatServer::class);
    }

    public function add(ChatServer $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ChatServer $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    // canal1 -> principal nivel modulo o  principal a nivel de seccion de modulo
    //      canal 1.1   -> parentId

    // crearCanal
    //      serverId
    //          -> seccion foro
    //
    //              serverId, name => canal principal
    //              parentId = null, name -> retorno el id

    // direct -> canal principal con tipo direct
    //      usuario -> tutor = subcanal tipo direct -> parentId canal principal direct
    // forum -> canal principal tipo forum
    //      subchannels -> parent forum principal, tipo = null
    /**
     * @param string $entityId
     *
     * @return ChatServer|array|object|null
     */
    public function getServer(string $type, int $entityId, bool $entity = false)
    {
        $server = $this->_em->getRepository(ChatServer::class)->findOneBy([
            'type' => $type,
            'entityId' => $entityId
        ]);

        if (!$server) {
            $server = new ChatServer();
            $server->setType($type)
                ->setEntityId((string) $entityId);

            $this->_em->persist($server);
            $this->_em->flush();
        }

        if ($entity) {
            return $server;
        }

        /** @var ChatChannel[] $channelsData */
        $channelsData = $this->_em->getRepository(ChatChannel::class)
            ->createQueryBuilder('cc')
            ->where('cc.server =:server')
            ->andWhere('cc.parent is null')
            ->setParameter('server', $server)
            ->getQuery()
            ->getResult();

        $channels = [];
        foreach ($channelsData as $channel) {
            $childChannels = $channel->getChatChannels();
            $children = [];
            foreach ($childChannels as $c) {
                $children[] = [
                    'id' => $c->getId(),
                    'type' => $c->getType(),
                    'name' => $c->getName(),
                ];
            }
            $channels[] = [
                'id' => $channel->getId(),
                'type' => $channel->getType(),
                'name' => $channel->getName(),
                'channels' => $children
            ];
        }

        return [
            'id' => $server->getId(),
            'channels' => $channels
        ];
    }
}
