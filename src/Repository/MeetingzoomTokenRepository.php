<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\MeetingzoomToken;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MeetingzoomToken>
 *
 * @method MeetingzoomToken|null find($id, $lockMode = null, $lockVersion = null)
 * @method MeetingzoomToken|null findOneBy(array $criteria, array $orderBy = null)
 * @method MeetingzoomToken[]    findAll()
 * @method MeetingzoomToken[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MeetingzoomTokenRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MeetingzoomToken::class);
    }

    public function add(MeetingzoomToken $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(MeetingzoomToken $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
