<?php

declare(strict_types=1);

namespace App\Service\Diploma;

use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\DateFormatter\DateFormatterService;
use App\Service\Diploma\Strategy\DhlStrategy;
use App\Service\Diploma\Strategy\DiplomaInterface;
use App\Service\Diploma\Strategy\EasylearningStrategy;
use App\Service\Diploma\Strategy\FundaeStrategy;
use App\Service\Diploma\Strategy\HobetuzStrategy;
use App\Service\Diploma\Strategy\NovomaticStrategy;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class DiplomaFactory
{
    private array $strategyMap = [
        'Fundae' => FundaeStrategy::class,
        'Easylearning' => EasylearningStrategy::class,
        'Hobetuz' => HobetuzStrategy::class,
        'Dhl' => DhlStrategy::class,
        'Novomatic' => NovomaticStrategy::class,
    ];

    public function __construct(
        protected EntityManagerInterface $em,
        protected SettingsService $settings,
        protected AnnouncementConfigurationsService $announcementConfiguration,
        protected DateFormatterService $dateFormatter
    ) {
    }

    /**
     * @throws \InvalidArgumentException When diploma type is unknown
     */
    public function createDiploma(string $diplomaType): DiplomaInterface
    {
        if (!\array_key_exists($diplomaType, $this->strategyMap)) {
            throw new \InvalidArgumentException("Unknown diploma type: $diplomaType");
        }

        $strategyClass = $this->strategyMap[$diplomaType];

        return new $strategyClass($this->em, $this->settings, $this->announcementConfiguration, $this->dateFormatter);
    }

    /**
     * Get a strategy instance by name (alias for createDiploma for testing purposes).
     *
     * @throws \InvalidArgumentException When diploma strategy is unknown
     */
    public function getStrategy(string $strategyName): DiplomaInterface
    {
        if (!\array_key_exists($strategyName, $this->strategyMap)) {
            throw new \InvalidArgumentException("Unknown diploma strategy: $strategyName");
        }

        $strategyClass = $this->strategyMap[$strategyName];

        return new $strategyClass($this->em, $this->settings, $this->announcementConfiguration, $this->dateFormatter);
    }
}
