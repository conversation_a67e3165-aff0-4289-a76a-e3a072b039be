<?php

declare(strict_types=1);

namespace App\Service\Annoucement\ReportPdf\Pdf;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\UserCourse;
use App\Service\Annoucement\Admin\AnnouncementGroupService;
use App\Service\Annoucement\ReportPdf\BaseFundaeReportGenerator;
use App\V2\Infrastructure\Utils\MpdfFactory;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Twig\Environment as TwigEnvironment;

class PdfAnnouncementGroupService extends BaseFundaeReportGenerator
{
    protected EntityManagerInterface $em;
    protected MpdfFactory $mpdfFactory;
    protected TwigEnvironment $twig;
    protected LoggerInterface $logger;
    protected $announcementGroupService;

    public function __construct(
        EntityManagerInterface $em,
        MpdfFactory $mpdfFactory,
        TwigEnvironment $twig,
        LoggerInterface $logger,
        AnnouncementGroupService $announcementGroupService
    ) {
        parent::__construct($em, $mpdfFactory, $twig, $logger);
        $this->announcementGroupService = $announcementGroupService;
    }

    public function generatePdfGroup(?AnnouncementGroup $announcementGroup = null, Announcement $announcement, $fullPath = null)
    {
        return $this->generatePdfContent($announcementGroup, $announcement, $fullPath);
    }

    private function getAnnouncementUsers(Announcement $announcement)
    {
        $announcementUserRepository = $this->em->getRepository(AnnouncementUser::class);
        $announcementUsers = $announcementUserRepository->findBy(['announcement' => $announcement]);

        return $announcementUsers;
    }

    private function generatePdfContent(?AnnouncementGroup $announcementGroup = null, Announcement $announcement, $fullPath = null)
    {
        $userCourses = [];
        $userChapters = [];
        $group = $this->announcementGroupService->getStudentsByGroup($announcementGroup, $announcement);

        if ($announcementGroup) {
            $announcementUsers = $announcementGroup->getAnnouncementUsers();
        } else {
            $announcementUsers = $this->getAnnouncementUsers($announcement);
        }

        foreach ($announcementUsers as $announcementUser) {
            $userCourse = $this->em->getRepository(UserCourse::class)->findOneBy([
                'announcement' => $announcement,
                'user' => $announcementUser->getUser(),
            ]);

            $userCourses[$announcementUser->getUser()->getId()] = $userCourse;

            if ($userCourse) {
                foreach ($userCourse->getChapters() as $chapter) {
                    $userChapters[$announcementUser->getUser()->getId()][$chapter->getChapter()->getId()] = $chapter;
                    if (date_format($chapter->getStartedAt(), 'Y-m-d') == date_format($chapter->getStartedAt(), 'Y-m-d')) {
                        $timeByDay[$announcementUser->getUser()->getId()][] = date_format($chapter->getStartedAt(), 'Y-m-d');
                    }
                }
            }
        }

        $mpdf = $this->generate(
            'fundae/report_group/main.html.twig',
            [
                'announcement' => $announcement,
                'group' => $group,
                'userCourses' => $userCourses,
                'userChapters' => $userChapters,
            ]
        );

        if (!empty($fullPath)) {
            $mpdf->Output($fullPath, 'F');
        }

        return $mpdf;
    }
}
