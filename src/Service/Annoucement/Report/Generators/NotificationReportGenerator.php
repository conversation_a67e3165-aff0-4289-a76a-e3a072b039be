<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report\Generators;

use App\Service\Annoucement\Report\AnnouncementContainer;
use App\Service\Annoucement\Report\AnnouncementReportConstants;
use App\Service\Annoucement\Report\AnnouncementReportDataService;
use App\Utils\SpreadsheetUtil;
use Psr\Log\LoggerInterface;

class NotificationReportGenerator
{
    private AnnouncementReportDataService $announcementReportDataService;
    private AnnouncementReportConstants $announcementReportConstants;
    private LoggerInterface $logger;

    public function __construct(
        AnnouncementReportDataService $announcementReportDataService,
        AnnouncementReportConstants $announcementReportConstants,
        LoggerInterface $logger
    ) {
        $this->announcementReportDataService = $announcementReportDataService;
        $this->announcementReportConstants = $announcementReportConstants;
        $this->logger = $logger;
    }

    public function generate(
        AnnouncementContainer $announcementContainer,
        string $announcementDir,
        callable $initSheetCallback,
        bool $isOnlineMode
    ): void {
        $headersInfoNotification = $isOnlineMode ?
            AnnouncementReportConstants::NOTIFICATIONS_HEADERS['info'] :
            [];

        $report = new SpreadsheetUtil('Reporte de notificaciones', 'Info de notificaciones');

        $initSheetCallback($report, 'Info de notificaciones', $headersInfoNotification);
        $dataInfoNotification = $this->announcementReportDataService->notification_infoSheetData(
            $announcementContainer->announcement,
            $announcementContainer->announcementGroups
        );

        $report->fromArray($dataInfoNotification, '--', 'A2')->alignAllLeft();

        $report->saveReport($announcementDir);
    }
}
