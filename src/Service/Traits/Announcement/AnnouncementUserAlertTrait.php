<?php

namespace App\Service\Traits\Announcement;

use App\Entity\AnnouncementUser;
use App\Utils\DateCalculationUtils;


trait AnnouncementUserAlertTrait
{
    public function getAlertsUser(AnnouncementUser $announcementUser)
    {
        $alerts = [];

        $alerts[] = $this->getAlert($announcementUser, 'getAlertCourseNotCompletedFinalDays', 10);
        $alerts[] = $this->getAlert($announcementUser, 'getAlertNoAccessCourse');
        $alerts[] = $this->getAlert($announcementUser, 'getAlertCourseNotCompletedLowProgress');
        $alerts[] = $this->getAlert($announcementUser, 'getAlertCourseNotCompletedMediumProgress');
        $alerts[] = $this->getAlert($announcementUser, 'getAlertPersonNotRespondedSurvey');
        $alerts[] = $this->getAlert($announcementUser, 'getAlertPersonNotDownloadedDiploma');

        // Agregar más tipos de alertas aquí si es necesario

        return array_filter($alerts); // Elimina los valores nulos del array
    }

    public function getAlert(AnnouncementUser $announcementUser, string $alertMethod, ...$args)
    {
        if (method_exists($this, $alertMethod)) {
            return $this->$alertMethod($announcementUser, ...$args);
        }

        return null;
    }

    public function getAlertNoAccessCourse(AnnouncementUser $announcementUser)
    {
        $announcement = $announcementUser->getAnnouncement();
        if (!$this->annnouncementAlertTutorService->hasPersonNotAccessedCourse($announcement)) return null;
       
        $conexionUser = $this->getConexionsAnnouncementUser($announcementUser);

        $startDate = $announcement->getStartAt();

        if ($conexionUser == null && $startDate < new \DateTime()) {
            return  $this->annnouncementAlertTutorService->getAlertsValues()['PERSON_NOT_ACCESSED_COURSE'];
        }

        return null;
    }

    public function getAlertCourseNotCompletedLowProgress(AnnouncementUser $announcementUser)
    {
        $announcement = $announcementUser->getAnnouncement();
        if (!$this->annnouncementAlertTutorService->hasCourseNotCompletedLowProgress($announcement)) return null;

        $startDate =  $announcement->getStartAt();
        $finishDate =  $announcement->getFinishAt();

        $progressAnnouncement = DateCalculationUtils::calculatePercentageProgress($startDate, $finishDate);
        $progressTotalUser = $this->getProgressTotal($announcementUser);

        if (($progressAnnouncement >= 50 && $progressAnnouncement < 60) && ($progressTotalUser < 25)) {
            return  $this->annnouncementAlertTutorService->getAlertsValues()['COURSE_NOT_COMPLETED_LOW_PROGRESS'];
        }

        return null;
    }

    public function getAlertCourseNotCompletedMediumProgress(AnnouncementUser $announcementUser)
    {
        $announcement = $announcementUser->getAnnouncement();
        if (!$this->annnouncementAlertTutorService->hasCourseNotCompletedMediumProgress($announcement)) return null;

        $startDate =  $announcement->getStartAt();
        $finishDate =  $announcement->getFinishAt();

        $progressAnnouncement = DateCalculationUtils::calculatePercentageProgress($startDate, $finishDate);
        $progressTotalUser = $this->getProgressTotal($announcementUser);

        if (($progressAnnouncement >= 80 && $progressAnnouncement < 90) && ($progressTotalUser < 50)) {
            return  $this->annnouncementAlertTutorService->getAlertsValues()['COURSE_NOT_COMPLETED_MEDIUM_PROGRESS'];
        }

        return null;
    }

    public function getAlertCourseNotCompletedFinalDays(AnnouncementUser $announcementUser)
    {
        $announcement = $announcementUser->getAnnouncement();
        if (!$this->annnouncementAlertTutorService->hasCourseNotCompletedFinalDays($announcement)) return null;

        $startDate =  $announcement->getStartAt();
        $finishDate =  $announcement->getFinishAt();

        // A esto se le pasa el porcentaje de finalización del curso, por ejemplo si falta menos del 15%
        $isEndingCourse = DateCalculationUtils::isEndingSoon($startDate, $finishDate, 10);
        $progressAnnouncement = DateCalculationUtils::calculatePercentageProgress($startDate, $finishDate);
        $progressTotal = $this->getProgressTotal($announcementUser);

        if ($finishDate > new \DateTime()) return null;

        if ($isEndingCourse && $progressTotal < $progressAnnouncement) {
            return  $this->annnouncementAlertTutorService->getAlertsValues()['COURSE_NOT_COMPLETED_FINAL_DAYS'];
        }

        return null;
    }

    public function getAlertPersonNotRespondedSurvey(AnnouncementUser $announcementUser)
    {
        if(!$this->announcementConfigurationsService->hasSurvey($announcementUser->getAnnouncement())) return null;

        $announcement = $announcementUser->getAnnouncement();
        if (!$this->annnouncementAlertTutorService->hasPersonNotRespondedSurvey($announcement)) return null;
        $isCompletedCourse = $announcementUser->isAproved();

        if (!$isCompletedCourse) return null;

        if (!$this->hasSurveyCompleted($announcementUser) && $isCompletedCourse) {
            return  $this->annnouncementAlertTutorService->getAlertsValues()['PERSON_NOT_RESPONDED_SURVEY'];
        }

        return null;
    }

    public function getAlertPersonNotDownloadedDiploma(AnnouncementUser $announcementUser)
    {
        $hasDiploma = $this->announcementConfigurationsService->hasDiploma($announcementUser->getAnnouncement());
        if(!$hasDiploma) return null;

        $hasSurvey = $this->announcementConfigurationsService->hasSurvey($announcementUser->getAnnouncement());
        $announcement = $announcementUser->getAnnouncement();
        if (!$this->annnouncementAlertTutorService->hasPersonNotDownloadedDiploma($announcement)) return null;
        $isCompletedCourse = $announcementUser->isAproved();

        if($hasSurvey) $isCompletedCourse = $announcementUser->isAproved() && $this->hasSurveyCompleted($announcementUser);

        if (!$isCompletedCourse) return null;

        if ($isCompletedCourse && !$this->confirmDownloadedDiploma($announcementUser)) {
            return  $this->annnouncementAlertTutorService->getAlertsValues()['PERSON_NOT_DOWNLOADED_DIPLOMA'];
        }

        return null;
    }
}
