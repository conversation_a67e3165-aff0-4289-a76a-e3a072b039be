<?php

namespace App\Entity;

use App\Repository\NewsRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=NewsRepository::class)
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 * @ORM\HasLifecycleCallbacks()
 * @Vich\Uploadable()
 */
class News implements TranslatableInterface
{
    use AtAndBy;
    use Imageable;
    use TranslatableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"news", "course_section:detail"})
     */
    private $id;

    /**
     * @ORM\Column(type="text")
     * @Groups({"news", "course_section:detail"})
     */
    private $title;

    /**
     * @ORM\Column(type="text")
     * @Groups({"news", "course_section:detail"})
     */
    private $text;

    /**
     * @ORM\Column(type="string", length=10)
     */
    private $locale;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $image;

    /**
     * @Vich\UploadableField(mapping="news_image", fileNameProperty="image")
     */
    private $imageFile;

    /**
     * @var string
     * @Groups({"news"})
     */
    private $imageUrl;

    /**
     * @ORM\ManyToMany(targetEntity=CourseSection::class, inversedBy="news")
     */
    private $courseSection;

    public function __construct()
    {
        $this->courseSection = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->getTitle();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getText(): ?string
    {
        return $this->text;
    }

    public function setText(string $text): self
    {
        $this->text = $text;

        return $this;
    }

    public function getLocale(): ?string
    {
        return $this->locale;
    }

    public function setLocale(string $locale): self
    {
        $this->locale = $locale;

        return $this;
    }

    /**
     * @return Collection<int, CourseSection>
     */
    public function getCourseSection(): Collection
    {
        return $this->courseSection;
    }

    public function addCourseSection(CourseSection $courseSection): self
    {
        if (!$this->courseSection->contains($courseSection)) {
            $this->courseSection[] = $courseSection;
        }

        return $this;
    }

    public function removeCourseSection(CourseSection $courseSection): self
    {
        $this->courseSection->removeElement($courseSection);

        return $this;
    }
}
