<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\ZipFileTaskRepository;
use Doctrine\ORM\Mapping as ORM;
use <PERSON>ed<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * @ORM\HasLifecycleCallbacks()
 *
 * @ORM\Entity(repositoryClass=ZipFileTaskRepository::class)
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class ZipFileTask
{
    use AtAndBy;
    public const STATUS_PENDING = 0;
    public const STATUS_IN_PROGRESS = 1;
    public const STATUS_COMPLETED = 2;
    public const STATUS_FAILED = -1;
    public const STATUS_TIMEOUT = -2;

    public $statusInfo = [
        '-1' => 'STATUS_INFORMATION.FAILED',
        '-2' => 'STATUS_INFORMATION.TIMEOUT',
        '0' => 'STATUS_INFORMATION.PENDING',
        '1' => 'STATUS_INFORMATION.IN_PROGRESS',
        '2' => 'STATUS_INFORMATION.COMPLETED'
    ];

    public function getStatusAsString(): string
    {
        return $this->statusInfo['' . $this->getStatus()];
    }

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * Based on per module logic.
     *
     * @ORM\Column(type="string", length=20)
     */
    private $type;

    /**
     * Based on per module logic.
     *
     * @ORM\Column(type="string", length=10, nullable=true)
     */
    private $entityId;

    /**
     * @ORM\Column(type="json")
     */
    private array $params = [];

    /**
     * @ORM\Column(type="string", length=255)
     */
    private ?string $task;

    /**
     * @ORM\Column(type="smallint")
     */
    private int $status;

    /**
     * Filename in the server
     * Once generated this will be the path in relation with project dir:
     *
     * @ORM\Column(type="text", nullable=true)
     */
    private $filename;

    /**
     * The name used for the download.
     *
     * @ORM\Column(type="text", nullable=true)
     */
    private $originalName;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=true)
     */
    private $startedAt;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=true)
     */
    private $finishedAt;

    /**
     * @ORM\ManyToOne(targetEntity=FilesManager::class)
     */
    private $filesManager;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $available_at;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->available_at = new \DateTimeImmutable('+5 days');
        $this->status = self::STATUS_PENDING;
        $this->task = null;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getEntityId(): ?string
    {
        return $this->entityId;
    }

    public function setEntityId(?string $entityId): self
    {
        $this->entityId = $entityId;

        return $this;
    }

    public function getParams(): ?array
    {
        return $this->params;
    }

    public function setParams(array $params): self
    {
        $this->params = $params;

        return $this;
    }

    public function getTask(): ?string
    {
        return $this->task;
    }

    public function setTask(string $task): self
    {
        $this->task = $task;

        return $this;
    }

    public function getStatus(): ?int
    {
        return $this->status;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getStartedAt(): ?\DateTimeImmutable
    {
        return $this->startedAt;
    }

    public function setStartedAt(?\DateTimeImmutable $startedAt): self
    {
        $this->startedAt = $startedAt;

        return $this;
    }

    public function getFinishedAt(): ?\DateTimeImmutable
    {
        return $this->finishedAt;
    }

    public function setFinishedAt(?\DateTimeImmutable $finishedAt): self
    {
        $this->finishedAt = $finishedAt;

        return $this;
    }

    public function getFilename(): ?string
    {
        if ($this->filename && false === strpos($this->filename, '.zip')) {
            return $this->filename . '.zip';
        }

        return $this->filename;
    }

    public function setFilename(?string $filename): self
    {
        $this->filename = empty($filename) ? (new \DateTimeImmutable())->getTimestamp() . '' : $filename;

        if (false === strpos($this->filename, '.zip')) {
            $this->filename .= '.zip';
        }

        return $this;
    }

    public function getOriginalName(): ?string
    {
        if (false === strpos($this->originalName, '.zip')) {
            return $this->originalName . '.zip';
        }

        return $this->originalName;
    }

    public function setOriginalName(?string $originalName): self
    {
        if (false === strpos($originalName, '.zip')) {
            $originalName .= '.zip';
        }

        $this->originalName = $originalName;

        return $this;
    }

    public function getFunction(): string
    {
        $functionName = '';
        $words = explode('-', $this->getTask());

        foreach ($words as $i => $word) {
            if ($i !== array_key_first($words)) {
                $word = ucfirst($word);
            }
            $functionName .= $word;
        }

        return $functionName;
    }

    public function getFilesManager(): ?FilesManager
    {
        return $this->filesManager;
    }

    public function setFilesManager(?FilesManager $filesManager): self
    {
        $this->filesManager = $filesManager;

        return $this;
    }

    public function getAvailableAt(): ?\DateTimeInterface
    {
        return $this->available_at;
    }

    public function setAvailableAt(?\DateTimeInterface $available_at): self
    {
        $this->available_at = $available_at;

        return $this;
    }
}
