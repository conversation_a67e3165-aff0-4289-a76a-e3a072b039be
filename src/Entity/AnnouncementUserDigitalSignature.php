<?php

namespace App\Entity;

use App\Behavior\Imageable;
use App\Behavior\Timestampable;
use App\Repository\AnnouncementUserDigitalSignatureRepository;
use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=AnnouncementUserDigitalSignatureRepository::class)
 * @Vich\Uploadable()
 */
class AnnouncementUserDigitalSignature
{
    use Timestampable;
    use Imageable;
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=AnnouncementUser::class, inversedBy="announcementUserDigitalSignatures")
     */
    private $announcementUser;


    /**
     * @ORM\ManyToOne(targetEntity=AnnouncementGroupSession::class, inversedBy="announcementUserDigitalSignatures")
     */
    private $announcementGroupSession;

    /**
     * @Vich\UploadableField(mapping="digital_signature", fileNameProperty="image")
     */
    private $imageFile;

    public function  __construct()
    {
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAnnouncementUser(): ?AnnouncementUser
    {
        return $this->announcementUser;
    }

    public function setAnnouncementUser(?AnnouncementUser $announcementUser): self
    {
        $this->announcementUser = $announcementUser;

        return $this;
    }

    public function getAnnouncementGroupSession(): ?AnnouncementGroupSession
    {
        return $this->announcementGroupSession;
    }

    public function setAnnouncementGroupSession(?AnnouncementGroupSession $announcementGroupSession): self
    {
        $this->announcementGroupSession = $announcementGroupSession;

        return $this;
    }
}
