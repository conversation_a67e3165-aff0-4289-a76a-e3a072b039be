<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\AnnouncementInspectorAccessRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * @ORM\Entity(repositoryClass=AnnouncementInspectorAccessRepository::class)
 */
class AnnouncementInspectorAccess implements UserInterface, PasswordAuthenticatedUserInterface
{
    public const EMAIL_INSPECTOR = '<EMAIL>';
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=45)
     */
    private ?string $user;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private ?string $password;

    /**
     * @ORM\Column(type="json")
     */
    private array $roles = [
        'ROLE_INSPECTOR',
    ];

    /**
     * @ORM\OneToOne(targetEntity=Announcement::class, inversedBy="announcementInspectorAccess", cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $announcement;

    /**
     * @ORM\OneToOne(targetEntity=UserToken::class, inversedBy="announcementInspectorAccess", cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $token;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?string
    {
        return $this->user;
    }

    public function setUser(string $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    public function getUsername(): string
    {
        return $this->user;
    }

    public function getRoles(): array
    {
        return $this->roles;
    }

    public function getSalt()
    {
        // TODO: Implement getSalt() method.
    }

    public function eraseCredentials()
    {
        // TODO: Implement eraseCredentials() method.
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function getToken(): ?UserToken
    {
        return $this->token;
    }

    public function setToken(UserToken $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function hasRole($role): bool
    {
        return \in_array($role, $this->getRoles());
    }

    public function isTutor(): bool
    {
        return $this->hasRole(User::ROLE_TUTOR);
    }

    public function isAdmin(): bool
    {
        return $this->hasRole(User::ROLE_ADMIN);
    }

    public function isManager(): bool
    {
        return $this->hasRole(User::ROLE_MANAGER);
    }

    public function getLocale(): string
    {
        return 'es';
    }

    public function getUserIdentifier(): string
    {
        return '';
    }

    public function getEmail(): string
    {
        return self::EMAIL_INSPECTOR;
    }
}
