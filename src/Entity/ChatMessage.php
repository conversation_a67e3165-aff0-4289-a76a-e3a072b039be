<?php

namespace App\Entity;

use App\Repository\ChatMessageRepository;
use App\Utils\TimeZoneConverter\UtcTimezoneInterface;
use App\Utils\TimeZoneConverter\UtcTimezoneTrait;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ChatMessageRepository::class)
 */
class ChatMessage implements UtcTimezoneInterface
{
    use UtcTimezoneTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=ChatChannel::class, inversedBy="chatMessages")
     * @ORM\JoinColumn(nullable=false)
     */
    private $channel;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="chatMessages")
     * @ORM\JoinColumn(nullable=false)
     */
    private $user;

    /**
     * @ORM\Column(type="text")
     */
    private $message;

    /**
     * Reply to a message
     * @ORM\ManyToOne(targetEntity=ChatMessage::class, inversedBy="chatMessages")
     */
    private $replyTo;

    /**
     * All reply messages
     * @ORM\OneToMany(targetEntity=ChatMessage::class, mappedBy="replyTo")
     */
    private $chatMessages;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $seen = false;

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private $updatedAt;

    /**
     * @ORM\OneToMany(targetEntity=ChatMessageLike::class, mappedBy="chatMessage", orphanRemoval=true)
     */
    private $chatMessageLikes;

    /**
     * @ORM\OneToMany(targetEntity=ChatMessageReport::class, mappedBy="message", orphanRemoval=true)
     */
    private $chatMessageReports;

    public function __construct()
    {
        $this->chatMessages = new ArrayCollection();
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
        $this->chatMessageLikes = new ArrayCollection();
        $this->chatMessageReports = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getChannel(): ?ChatChannel
    {
        return $this->channel;
    }

    public function setChannel(?ChatChannel $channel): self
    {
        $this->channel = $channel;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getReplyTo(): ?self
    {
        return $this->replyTo;
    }

    public function setReplyTo(?self $replyTo): self
    {
        $this->replyTo = $replyTo;

        return $this;
    }

    /**
     * @return Collection<int, self>
     */
    public function getChatMessages(): Collection
    {
        return $this->chatMessages;
    }

    public function addChatMessage(self $chatMessage): self
    {
        if (!$this->chatMessages->contains($chatMessage)) {
            $this->chatMessages[] = $chatMessage;
            $chatMessage->setReplyTo($this);
        }

        return $this;
    }

    public function removeChatMessage(self $chatMessage): self
    {
        if ($this->chatMessages->removeElement($chatMessage)) {
            // set the owning side to null (unless already changed)
            if ($chatMessage->getReplyTo() === $this) {
                $chatMessage->setReplyTo(null);
            }
        }

        return $this;
    }

    public function isSeen(): ?bool
    {
        return $this->seen;
    }

    public function setSeen(bool $seen): self
    {
        $this->seen = $seen;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeImmutable $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * @return Collection<int, ChatMessageLike>
     */
    public function getChatMessageLikes(): Collection
    {
        return $this->chatMessageLikes;
    }

    public function addChatMessageLike(ChatMessageLike $chatMessageLike): self
    {
        if (!$this->chatMessageLikes->contains($chatMessageLike)) {
            $this->chatMessageLikes[] = $chatMessageLike;
            $chatMessageLike->setChatMessage($this);
        }

        return $this;
    }

    public function removeChatMessageLike(ChatMessageLike $chatMessageLike): self
    {
        if ($this->chatMessageLikes->removeElement($chatMessageLike)) {
            // set the owning side to null (unless already changed)
            if ($chatMessageLike->getChatMessage() === $this) {
                $chatMessageLike->setChatMessage(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ChatMessageReport>
     */
    public function getChatMessageReports(): Collection
    {
        return $this->chatMessageReports;
    }

    public function addChatMessageReport(ChatMessageReport $chatMessageReport): self
    {
        if (!$this->chatMessageReports->contains($chatMessageReport)) {
            $this->chatMessageReports[] = $chatMessageReport;
            $chatMessageReport->setMessage($this);
        }

        return $this;
    }

    public function removeChatMessageReport(ChatMessageReport $chatMessageReport): self
    {
        if ($this->chatMessageReports->removeElement($chatMessageReport)) {
            // set the owning side to null (unless already changed)
            if ($chatMessageReport->getMessage() === $this) {
                $chatMessageReport->setMessage(null);
            }
        }

        return $this;
    }

    public function __toString()
    {
        return $this->getId() . '';
    }
}
