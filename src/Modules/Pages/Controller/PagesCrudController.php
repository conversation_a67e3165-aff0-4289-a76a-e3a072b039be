<?php

namespace App\Modules\Pages\Controller;

use App\Entity\Pages;
use App\Repository\PagesRepository;

use App\Modules\Pages\Services\PagesCrudService;

use App\Modules\Common\Controller\BaseVueController;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Constraints\IsFalse;
use Symfony\Contracts\Translation\TranslatorInterface;


/**
 * @Route("/admin/")
 */
class PagesCrudController extends BaseVueController 
{

    private AdminContextProvider $context;
    private JWTManager $JWTManager;
    private PagesCrudService $pagesCrudService;
    private PagesRepository $pagesRepository;

    public function __construct(
        AdminContextProvider $context,
        JWTManager $JWTManager,
        SettingsService $settings,
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        PagesCrudService $pagesCrudService,
        TranslatorInterface $translator,
        PagesRepository $pagesRepository
    ) {

        parent::__construct($settings, $em, $logger, $requestStack, $translator);

        $this->context = $context;
        $this->JWTManager = $JWTManager;
        $this->pagesCrudService = $pagesCrudService;
        $this->pagesRepository = $pagesRepository;
    }

    public static function getEntityFqcn(): string
    {
        return PAGES::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud->overrideTemplate('crud/index', 'admin/pages/app.html.twig');
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_INDEX ===  $responseParameters->get('pageName')) {
            $this->configureAppResponseParameters(
                $responseParameters,
                $this->settings,
                $this->context,
                $this->JWTManager,
                [],
            );
        }
        return $responseParameters;
    }

    /**
     * @Rest\Get("pages/all")
     * @return Response
     */
    public function getAllPages()
    {
        return $this->executeSafe(function () {
            $pages = $this->em->getRepository(PAGES::class)->findAll();            
            $data = [];
            foreach ($pages as $page) {
                $data[] = $this->pagesCrudService->formatPagesStructure($page);
            }
            
            return  [
                'pages' => $data,
                'languages' => $this->settings->get('app.languages'),  
            ];
        });
    }

    /**
     * @Rest\Put("pages/{id}/active")
     * @param PAGES $pages
     * @return Response
     */
    public function changeStatusPages(PAGES $pages)
    {
        return $this->executeSafe(function () use ($pages) {
           $this->pagesCrudService->changeStatePages($pages);

            return $this->pagesCrudService->formatPagesStructure($pages);
        }, [], [], Response::HTTP_OK);
    }

    /**
     * @Rest\Put("pages/{id}/change-is-main")
     * @param PAGES $pages
     * @return Response
     */
    public function changeIsMainPages(PAGES $pages): Response
    {
        return $this->executeSafe(function () use ($pages) {
            $foundMain = $this->em->getRepository(PAGES::class)->findOneBy(['main' => true]);
            if ($foundMain) $this->pagesCrudService->changeIsMainPages($foundMain, false);

            $this->pagesCrudService->changeIsMainPages($pages, true);

            return [
                'newSectionMain' => $this->pagesCrudService->formatPagesStructure($pages),
                'oldSectionMain' =>  $foundMain ? $this->pagesCrudService->formatPagesStructure($foundMain) : null
            ];
        }, [], [], Response::HTTP_OK);
    }

    /**
     * @Rest\Post("pages/create")
     * @param Request $request
     * @return Response
     */
    public function createCourseSection()
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $pages = new PAGES();

            if (($result = $this->savePages($request, $pages)) instanceof Response) return $result;
            
            return $this->pagesCrudService->formatPagesStructure($pages);
        }, [], [], Response::HTTP_CREATED);
    }

    /**
     * @Rest\Post("pages/update")
     * @param Request $request
     * @param PagesRepository $pagesRepository
     * @return Response
     */
    public function updateCourseSection(PagesRepository $pagesRepository)
    {
        return $this->executeSafe(function () use ($pagesRepository) {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $id = $content['id'] ?? -1;
            $pages = $pagesRepository->find($id);

            if (!$pages) return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);

            if (($result = $this->savePages($request, $pages)) instanceof Response) return $result;
           
            return $this->pagesCrudService->formatPagesStructure($pages);
        }, [], [], Response::HTTP_OK);

    }

    /**
     * @Rest\Delete("pages/{id}/delete")
     * @return Response
     */
    public function deleteCourseSection(PAGES $pages): Response
    {
        return $this->executeSafe(function () use ($pages): void {
            $this->pagesCrudService->delTranslationPages($pages);
            $this->em->remove($pages);
            $this->em->flush();
        }, [], [], Response::HTTP_NO_CONTENT);
    }

    public function savePages(Request $request, PAGES $pages)
    {
        $content = json_decode($request->getContent(), true);

        $name = $content['name'] ?? null;
        $description = $content['description'] ?? null;
        $position = $content['position'] ?? null;
        $active = $content['active'] ?? false;
        $isMain = false;

        if (!$position)
            $position = $this->pagesRepository->getNextPosition();

        $errors = [];
        if (is_null($name)) $errors[] = 'Name required';
        if (count($errors)) return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => true,
            'data' => $errors
        ]);

        $pages->setName($name)
            ->setDescription($description)
            ->setActive($active)
            ->setPosition($position)
            ->setMain($isMain);
        $this->pagesCrudService->savePagesTranslation($content['translations'], $pages);

        $this->em->persist($pages);
        $this->em->flush();
        return true;
    }

    /**
     * @Rest\Put("pages/changePosition")
     * @param Request $request
     */
    public function changePagesPosition()
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $pages = json_decode($request->getContent(), true)["pages"];

            foreach ($pages as $page) {
                $pageBD = $this->em->getRepository(PAGES::class)->find($page['id']);
                $pageBD->setPosition($page['newSort']);
                $this->em->persist($pageBD);
            }

            $this->em->flush();
            return $this->pagesCrudService->formatPagesStructure($pageBD);
        }, [], [], Response::HTTP_OK);
    }


}
