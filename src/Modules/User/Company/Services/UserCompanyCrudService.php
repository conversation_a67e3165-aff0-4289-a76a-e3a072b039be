<?php

declare(strict_types=1);

namespace App\Modules\User\Company\Services;

use App\Entity\UserCompany;
use App\Modules\Common\Services\BaseService;

class UserCompanyCrudService extends BaseService
{
    public function formatUserCompanyStructure(UserCompany $userCompany): array
    {
        return [
            'id' => $userCompany->getId(),
            'name' => $userCompany->getName(),
            'description' => $userCompany->getDescription(),
            'state' => $userCompany->getState(),
            'cif' => $userCompany->getCif(),
            'profile' => $userCompany->getProfile(),
            'code' => $userCompany->getCode(),
            'active' => $userCompany->isActive(),
        ];
    }

    public function setUserCompanyData(UserCompany $userCompany, array $data): array
    {
        $name = $data['name'] ?? null;
        $description = $data['description'] ?? null;
        $state = $data['state'] ?? false;
        $cif = $data['cif'] ?? null;
        $profile = $data['profile'] ?? null;
        $code = $data['code'] ?? null;

        $userCompany->setName($name)
            ->setDescription($description)
            ->setState((int) $state)
            ->setCif($cif)
            ->setProfile($profile)
            ->setCode($code)
            ->setExternal(false)
            ->setActive(true);

        $this->em->persist($userCompany);

        $this->em->flush();

        return ['error' => false, 'data' => $userCompany];
    }
}
