<?php

declare(strict_types=1);

namespace App\Campus\Controller\General;

use App\Entity\Bots;
use App\Entity\Challenge;
use App\Entity\ChallengeAnswers;
use App\Entity\ChallengeDuel;
use App\Entity\ChallengeDuelQuestions;
use App\Entity\ChallengeDuelQuestionsAdn;
use App\Entity\ChallengeQuestions;
use App\Entity\ChallengeUser;
use App\Entity\ChallengeUserPoints;
use App\Entity\User;
use App\Repository\AnnouncementRepository;
use App\Repository\ChallengeRepository;
use App\Repository\CourseRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Swagger\Annotations as SWG;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Route("/api")
 */
class ApiChallengeController extends ApiBaseController
{
    private EntityManagerInterface $em;
    private ChallengeRepository $challengeRepository;

    private const int USER_CHALLENGER = 1;
    private const int USER_CHALLENGED = 2;

    /**
     * ApiController constructor.
     */
    public function __construct(LoggerInterface $logger, UserRepository $userRepository, CourseRepository $courseRepository, ChallengeRepository $challengeRepository, EntityManagerInterface $em, AnnouncementRepository $announcementRepository, TranslatorInterface $translator, SettingsService $settings)
    {
        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settings);
        $this->em = $em;
        $this->challengeRepository = $challengeRepository;
    }

    private function checkIfEnded($challenge): bool
    {
        $endDate = $challenge->getEndDate();
        $today = new \DateTime('NOW');

        if ($today < $endDate) {
            return false;
        }

        return true;
    }

    /**
     * @Rest\Get("/challenge/{id}/info", name="api_challengeinfo_get")
     */
    public function getChallengeInfo(Challenge $challenge): Response
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;
            if (!$this->checkIfEnded($challenge)) {
                $user = $this->getUser();
                $challengeDuelRepository = $this->em->getRepository(ChallengeDuel::class);
                $challengeAnswerRepository = $this->em->getRepository(ChallengeAnswers::class);

                // calculamos el % de preguntas acertadas
                $user1Answers = $challengeDuelRepository->getUserDuelByChallengeIdUser1($user, $challenge);
                $user2Answers = $challengeDuelRepository->getUserDuelByChallengeIdUser2($user, $challenge);
                $totalAnswers = array_merge($user1Answers, $user2Answers);
                $correctAnswers = 0;
                foreach ($totalAnswers as $answer) {
                    if (!\is_null($answer['id'])) {
                        $currrentAnswer = $challengeAnswerRepository->find($answer['id']);
                        if ($currrentAnswer->getCorrect()) {
                            ++$correctAnswers;
                        }
                    }
                }

                // calculamos el % de victorias
                $totalDuels = $challengeDuelRepository->getAllDuelsFinishedByChallengeByUser($user, $challenge);

                $percentWins = 0;
                if ($totalDuels) {
                    $duelWinAsUser1 = $challengeDuelRepository->getUserDuelWinByChallengeIdUser1($user, $challenge);
                    $duelWinAsUser2 = $challengeDuelRepository->getUserDuelWinByChallengeIdUser2($user, $challenge);
                    $totalWins = \count($duelWinAsUser1) + \count($duelWinAsUser2);
                    $percentWins = round($totalWins / $totalDuels * 100, 2);
                }

                $answersPercent = 0;
                if (0 !== \count($totalAnswers)) {
                    $answersPercent = round($correctAnswers / \count($totalAnswers) * 100, 2);
                }

                // calcular disponibles
                $duelAvailible = $this->settings->get('app.totalDuels') - $totalDuels;

                $payload = [
                    'challenge' => $challenge,
                    'duelAvailable' => $duelAvailible,
                    'duelDone' => $challengeDuelRepository->getAllDuelsByChallengeByUser($user, $challenge),
                    'percentWins' => $percentWins,
                    'answersPercent' => $answersPercent,
                    'maxDuel' => $this->settings->get('app.totalDuels'),
                ];
            } else {
                $payload = [
                    'ended' => true,
                ];
            }
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = 'An error has occurred trying to register the user - Error:' . $e->getMessage();
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $payload : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['challenge_info']]);
    }

    /**
     * @Rest\Get("/challenge/{id}/user/points", name="api_challengeuserpoints_get")
     */
    public function getUserChallengePoints(Challenge $challenge): Response
    {
        $pointsRepository = $this->em->getRepository(ChallengeUserPoints::class);
        $points = $pointsRepository->findOneBy(['challenge' => $challenge, 'user' => $this->getUser()]);

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $points,
        ];

        return $this->sendResponse($response, ['groups' => ['challenge_user_points']]);
    }

    /**
     * @Rest\Get("/challenge/{id}/ranking/{userPage}", name="api_challengeranking_get")
     */
    public function getChallengeRanking(Challenge $challenge, $userPage): Response
    {
        $pointsRepository = $this->em->getRepository(ChallengeUserPoints::class);
        $points = $pointsRepository->findBy(['challenge' => $challenge], ['points' => 'DESC'], null, 3);
        $podium = $pointsRepository->findBy(['challenge' => $challenge], ['points' => 'DESC'], 3);

        $data = [
            'offsets' => [
                'current' => 3,
                'last' => 10,
                'self' => 3,
            ],

            'podium' => $podium,

            'users' => $points,
        ];

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ];

        return $this->sendResponse($response, ['groups' => ['challenge_ranking']]);
    }

    /**
     * @Rest\Get("/challenge/{id}/simpleranking", name="api_challengesimpleranking_get")
     */
    public function getChallengeSimpleRanking(Challenge $challenge): Response
    {
        $pointsRepository = $this->em->getRepository(ChallengeUserPoints::class);
        $points = $pointsRepository->findBy(['challenge' => $challenge], ['points' => 'DESC'], 20);

        $data = [
            'points' => $points,
        ];

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ];

        return $this->sendResponse($response, ['groups' => ['challenge_ranking']]);
    }

    /**
     * @Rest\Get("/challenges/get", name="api_challenges")
     *
     * @SWG\Response(
     *     response=200,
     *     description="List of the challenges for the user"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="The user has no challenges"
     * )
     * @SWG\Response(
     *     response=500,
     *     description="Error"
     * )
     *
     * @SWG\Tag(name="Challenges")
     */
    public function challenges(): Response
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;

            $user = $this->getUser();
            $userChallengesRepository = $this->em->getRepository(ChallengeUser::class);
            $challengeDuelRepository = $this->em->getRepository(ChallengeDuel::class);
            $pointsRepository = $this->em->getRepository(ChallengeUserPoints::class);

            $userChallenges = $userChallengesRepository->getUserActiveChallenge($user);

            $maxDuel = $this->settings->get('app.totalDuels');

            if (!$userChallenges) {
                $challengesArray = [];
            } else {
                $challengesArray = [];
                foreach ($userChallenges as $challenge) {
                    $totalDuels = $challengeDuelRepository->getAllDuelsByChallengeByUser($user, $challenge->getChallenge());
                    $points = $pointsRepository->findOneBy(['challenge' => $challenge->getChallenge(), 'user' => $this->getUser()]);
                    $imageName = $challenge->getChallenge()->getImage();
                    $endDate = $challenge->getChallenge()->getEndDate();
                    \is_null($points) ? $userPoints = 0 : $userPoints = $points->getPoints();
                    \is_null($imageName) ? $imageRoute = null : $imageRoute = $this->settings->get('app.challenge_uploads_path') . '/' . $challenge->getChallenge()->getImage();
                    $today = new \DateTime('NOW');

                    $challengesArray[] = [
                        'challengeTitle' => $challenge->getChallenge()->getTitle(),
                        'challengeId' => $challenge->getChallenge()->getId(),
                        'maxDuel' => $maxDuel,
                        'duelDone' => $totalDuels,
                        'points' => $userPoints,
                        'image' => $imageRoute,
                        'active' => $today < $endDate,
                    ];
                }
            }
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = 'An error has occurred trying to get the callenges - Error:' . $e->getMessage();
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $challengesArray : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['userchallenge']]);
    }

    /**
     * @Rest\Get("/challenge/{id}/statistics/get", name="api_getstatistics")
     *
     * @SWG\Response(
     *     response=200,
     *     description="List of the statistics for the user"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="The user has no statistics"
     * )
     * @SWG\Response(
     *     response=500,
     *     description="Error"
     * )
     *
     * @SWG\Tag(name="userStatistics")
     */
    public function getUserStatisticsByChallengeId(Challenge $challenge): Response
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;
            $user = $this->getUser();

            $pointsRepository = $this->em->getRepository(ChallengeUserPoints::class);
            $points = $pointsRepository->findOneBy(['challenge' => $challenge, 'user' => $this->getUser()]);

            $userPoints = 0;
            if ($points) {
                $userPoints = $points->getPoints();
            }

            $challengeDuelRepository = $this->em->getRepository(ChallengeDuel::class);
            $history = $challengeDuelRepository->getUserHistoryByChallengeId($user, $challenge);

            $duelWinAsUser1 = $challengeDuelRepository->getUserDuelWinByChallengeIdUser1($user, $challenge);
            $duelWinAsUser2 = $challengeDuelRepository->getUserDuelWinByChallengeIdUser2($user, $challenge);

            $duelLoseAsUser1 = $challengeDuelRepository->getUserDuelLoseByChallengeIdUser1($user, $challenge);
            $duelLoseAsUser2 = $challengeDuelRepository->getUserDuelLoseByChallengeIdUser2($user, $challenge);

            $duelTie = $challengeDuelRepository->getUserDuelTieByChallengeId($user, $challenge);
            $duelLeft = $challengeDuelRepository->getUserDuelLeftByChallengeId($user, $challenge);

            $results = [];

            foreach ($history as $item) {
                $payload = [
                    'answers' => $this->getDuelStats($item),
                    'id' => $item->getId(),
                ];
                $results[] = $payload;
            }

            $duelStatsWin = \count($duelWinAsUser1) + \count($duelWinAsUser2);
            $duelStatsLost = \count($duelLoseAsUser1) + \count($duelLoseAsUser2);
            $duelStatsTie = \count($duelTie);
            $duelStatsPTE = $this->settings->get('app.totalDuels') - $duelStatsWin - $duelStatsLost - $duelStatsTie;

            $userData = [
                'points' => $userPoints,
                'challengeName' => $challenge->getTitle(),
                'duelWin' => $duelStatsWin,
                'duelLost' => $duelStatsLost,
                'dueltie' => $duelStatsTie,
                'duelLeft' => \count($duelLeft),
                'duelPte' => $duelStatsPTE,
                'userId' => $user->getId(),
                'result' => $results,
                'systemPoints' => $this->getPoints(),
            ];
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = 'An error has occurred trying to register the user - Error:' . $e->getMessage();
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $userData : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['challenge_user_statistics']]);
    }

    private function getDuelStats(ChallengeDuel $challengeDuel): array
    {
        $challengeDuelQuestionRepository = $this->em->getRepository(ChallengeDuelQuestions::class);
        $challengeAnswerRepository = $this->em->getRepository(ChallengeAnswers::class);
        $challengeDuelquestion = $challengeDuelQuestionRepository->findBy(['duel' => $challengeDuel]);

        $user1points = 0;
        $user2points = 0;
        $user1Info = $challengeDuel->getUser1();
        $user2Info = $challengeDuel->getUser2();
        $user1Percent = null;
        $user2Percent = null;
        $user1 = null;
        $user2 = null;
        $winner = null;
        $user1Time = 0;
        $user2Time = 0;

        foreach ($challengeDuelquestion as $item) {
            if (!\is_null($item->getAnswerUser1Id())) {
                $currrentAnswer = $challengeAnswerRepository->find($item->getAnswerUser1Id());
                if ($currrentAnswer->getCorrect()) {
                    ++$user1points;
                }
                $user1Time += $item->getTimeUser1();
            }

            if (!\is_null($item->getAnswerUser2Id())) {
                $currrentAnswer = $challengeAnswerRepository->find($item->getAnswerUser2Id());
                if ($currrentAnswer->getCorrect()) {
                    ++$user2points;
                }
                $user2Time += $item->getTimeUser2();
            }
        }

        0 === $user1points ? $user1Percent = 0 : $user1Percent = round(($user1points / \count($challengeDuelquestion)) * 100, 2);
        $user1 = [
            'info' => $user1Info,
            'percent' => $user1Percent,
            'time' => $user1Time,
        ];
        0 === $user2points ? $user2Percent = 0 : $user2Percent = round(($user2points / \count($challengeDuelquestion)) * 100, 2);

        $user2 = [
            'info' => $user2Info,
            'percent' => $user2Percent,
            'time' => $user2Time,
        ];

        return [
            'assigned_bot' => $challengeDuel->getAssignedBot(),
            'createdAt' => $challengeDuel->getUpdatedAt(),
            'user1' => $user1,
            'user2' => $user2,
            'winner' => $challengeDuel->getWinner(),
            'id' => $challengeDuel->getChallenge()->getId(),
        ];
    }

    /**
     * @Rest\Get("/challenge/duel/{id}/details", name="api_getdueldetails")
     *
     * @SWG\Response(
     *     response=200,
     *     description="Duel details"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="Error"
     * )
     * @SWG\Response(
     *     response=500,
     *     description="Error"
     * )
     *
     * @SWG\Tag(name="duelDetails")
     */
    public function getDuelDetails(ChallengeDuel $challengeDuel): Response
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;
            $user = $this->getUser();
            $challengeDuelQuestionRepository = $this->em->getRepository(ChallengeDuelQuestions::class);
            $challengeAnswerRepository = $this->em->getRepository(ChallengeAnswers::class);
            $challengeDuelquestion = $challengeDuelQuestionRepository->findBy(['duel' => $challengeDuel]);

            $user1points = 0;
            $user2points = 0;
            $user1Info = null;
            $user2Info = null;
            $user1Time = 0;
            $user2Time = 0;

            if ($challengeDuel->getUser1() === $user) {
                $userPosition = 1;
            } else {
                $userPosition = 2;
            }

            $questions = [];
            $user1Picks = [];
            $user2Picks = [];

            foreach ($challengeDuelquestion as $item) {
                /**
                 * @var $item ChallengeDuelQuestions
                 */
                if ($item->getQuestion()->getTranslations()->get($user->getLocale())) {
                    $item->getQuestion()->setText($item->getQuestion()->getTranslations()->get($user->getLocale())->getText());

                    foreach ($item->getQuestion()->getChallengeAnswers() as $challengeAnswer) {
                        if ($challengeAnswer->getTranslations()->get($user->getLocale())->getText()) {
                            $challengeAnswer->setText($challengeAnswer->getTranslations()->get($user->getLocale())->getText());
                        }
                    }
                }

                //                $questions[] = [
                //                    'questionText'      => $item->getQuestion(),
                //                    'correctAnswer'     => $challengeAnswerRepository->findBy(['pregunta'=>$item->getQuestion(), 'correct'=> 1]),
                //                ];

                $correctAnswer = $challengeAnswerRepository->findOneBy(['pregunta' => $item->getQuestion(), 'correct' => 1]);
                if ($correctAnswer->getTranslations()->get($user->getLocale())) {
                    $correctAnswer->setText($correctAnswer->getTranslations()->get($user->getLocale())->getText());
                }

                $questions[] = [
                    'questionText' => $item->getQuestion(),
                    'correctAnswer' => [$correctAnswer],
                ];

                if (!\is_null($item->getAnswerUser1Id())) {
                    $user1Info = $challengeDuel->getUser1();
                    $currrentAnswer = $challengeAnswerRepository->find($item->getAnswerUser1Id());
                    $user1Picks[] = [
                        'currentAnswer' => $currrentAnswer,
                        'questionId' => $item->getQuestion()->getId(),
                    ];
                    if ($currrentAnswer->getCorrect()) {
                        ++$user1points;
                    }
                    $user1Time += $item->getTimeUser1();
                }

                if (!\is_null($item->getAnswerUser2Id())) {
                    $user2Info = $challengeDuel->getUser2();
                    $currrentAnswer = $challengeAnswerRepository->find($item->getAnswerUser2Id());
                    $user2Picks[] = [
                        'currentAnswer' => $currrentAnswer,
                        'questionId' => $item->getQuestion()->getId(),
                    ];
                    if ($currrentAnswer->getCorrect()) {
                        ++$user2points;
                    }
                    $user2Time += $item->getTimeUser2();
                }
            }

            0 === $user1points ? $user1Percent = 0 : $user1Percent = round(($user1points / \count($challengeDuelquestion)) * 100, 2);
            $user1 = [
                'info' => $user1Info,
                'percent' => $user1Percent,
                'time' => $user1Time,
            ];

            0 === $user2points ? $user2Percent = 0 : $user2Percent = round(($user2points / \count($challengeDuelquestion)) * 100, 2);
            $user2 = [
                'info' => $user2Info,
                'percent' => $user2Percent,
                'time' => $user2Time,
            ];

            $duelDetail = [
                'user1' => $user1,
                'user2' => $user2,
                'winner' => $challengeDuel->getWinner(),
                'challengeId' => $challengeDuel->getChallenge()->getId(),
                'position' => $userPosition,
                'questions' => $questions,
                'user1Picks' => $user1Picks,
                'user2Picks' => $user2Picks,
                'points' => $this->getPoints(),
            ];
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = 'An error has occurred trying to register the user - Error:' . $e->getMessage();
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $duelDetail : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['duel_detail', 'challenge_users', 'duelQuestions']]);
    }

    /**
     * @Rest\Get("/challenge/{id}/createDuel", name="api_challenge_duelcreate")
     *
     * @SWG\Response(
     *     response=200,
     *     description="Crate or match duel"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="Error 404"
     * )
     * @SWG\Response(
     *     response=500,
     *     description="Error"
     * )
     *
     * @SWG\Tag(name="DuelCreate")
     */
    public function createDuel(Challenge $challenge): Response
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;

            if ($this->checkIfEnded($challenge)) {
                $response = [
                    'status' => $code,
                    'error' => true,
                    'data' => 'El duelo ha terminado',
                ];

                return $this->sendResponse($response);
            }

            $user = $this->getUser();
            // todo si el user 1 responde a una el sistema asigna el duelo al jugador 2 y no debería.
            $challengeDuelRepository = $this->em->getRepository(ChallengeDuel::class);
            $challengeDuelQuestionRepository = $this->em->getRepository(ChallengeDuelQuestions::class);
            $challengeQuestionsRepository = $this->em->getRepository(ChallengeQuestions::class);
            $challengeQuestionsAdnRepository = $this->em->getRepository(ChallengeDuelQuestionsAdn::class);

            $previousRivals = $challengeDuelRepository->getPreviousRivals($user, $challenge);
            \array_key_exists(0, $previousRivals) ? $rival1 = $previousRivals[0]['userId'] : $rival1 = 0;
            \array_key_exists(1, $previousRivals) ? $rival2 = $previousRivals[1]['userId'] : $rival2 = 0;
            $challengesAvailable = $challengeDuelRepository->findOpenedDuels($user, $challenge, $rival1, $rival2);
            $totalDuels = $challengeDuelRepository->getAllDuelsByChallengeByUser($user, $challenge);
            $duelAvailible = $this->settings->get('app.totalDuels') - $totalDuels;
            $getQuestionsFinal = [];

            if ($duelAvailible > 0) {
                if (!$challengesAvailable) {
                    $challengeDuel = new ChallengeDuel();
                    $challengeDuel->setChallenge($challenge)
                        ->setUser1($user);

                    $this->em->persist($challengeDuel);

                    $this->em->flush();
                } else {
                    $challengeDuelId = array_pop($challengesAvailable);
                    $challengeDuel = $challengeDuelRepository->find($challengeDuelId);
                    $challengeDuel->setUser2($user);
                    $this->em->persist($challengeDuel);

                    $this->em->flush();
                }

                if ($challengeDuel->getUser1() === $user || $challengeDuel->getUser2() === $user) {
                    $challengeDuelQuestionsCreated = $challengeDuelQuestionRepository->findBy(['duel' => $challengeDuel]);
                    if (!$challengeDuelQuestionsCreated) {
                        $totalCountChallengeQuestion = $challengeQuestionsRepository->getCountAllChallengeQuestions($challenge);
                        $userQuestionsAdn = $challengeQuestionsAdnRepository->findOneBy(['user' => $user, 'challenge' => $challenge]);

                        if (!$userQuestionsAdn) {
                            $userQuestionsAdn = new ChallengeDuelQuestionsAdn();
                            $userQuestionsAdn->setUser($user)
                                ->setChallenge($challenge)
                                ->setAdn([]);
                            $this->em->persist($userQuestionsAdn);
                            $this->em->flush();
                        }

                        $usedQuestions = $userQuestionsAdn->getAdn();

                        if (\count($usedQuestions) > ($totalCountChallengeQuestion - $this->settings->get('app.challengeloops'))) {
                            $usedQuestions = [];
                        }

                        $getRandomKeys = $challengeQuestionsRepository->getRandQuestion($challengeDuel->getChallenge(), $this->settings->get('app.challengeloops'), $usedQuestions);

                        foreach ($getRandomKeys as $key) {
                            $usedQuestions[] = $key->getId();
                            $currentQuestion = $challengeQuestionsRepository->find($key);
                            \is_null($currentQuestion->getImage()) ? $imageRoute = null : $imageRoute = $this->settings->get('app.challenge_question_uploads_path') . $currentQuestion->getImage();

                            $challengeDuelQuestions = new ChallengeDuelQuestions();
                            $challengeDuelQuestions->setDuel($challengeDuel)
                                ->setQuestion($currentQuestion);

                            // Init $answers array to avoid acumulation of answers
                            $answers = [];

                            if ($challengeDuelQuestions->getQuestion()->getTranslations()->get($user->getLocale())) {
                                $questionText = $challengeDuelQuestions->getQuestion()->getTranslations()->get($user->getLocale())->getText();
                                foreach ($challengeDuelQuestions->getQuestion()->getChallengeAnswers() as $answer) {
                                    if ($answer->getTranslations()->get($user->getLocale())) {
                                        $answers[] = [
                                            'id' => $answer->getId(),
                                            'text' => $answer->getTranslations()->get($user->getLocale())->getText(),
                                        ];
                                    } else {
                                        $answers[] = [
                                            'id' => $answer->getId(),
                                            'text' => $answer->getText(),
                                        ];
                                    }
                                }
                            } else {
                                $questionText = $challengeDuelQuestions->getQuestion()->getText();
                                foreach ($challengeDuelQuestions->getQuestion()->getChallengeAnswers() as $answer) {
                                    $answers[] = [
                                        'id' => $answer->getId(),
                                        'text' => $answer->getText(),
                                    ];
                                }
                            }

                            $getQuestionsFinal[] = [
                                'question' => $currentQuestion,
                                'answers' => $answers,
                                'imageQuestion' => $imageRoute,
                                'questionText' => $questionText,
                            ];
                            $this->em->persist($challengeDuelQuestions);
                        }
                        $userQuestionsAdn->setAdn($usedQuestions);
                        $this->em->persist($userQuestionsAdn);
                        $this->em->flush();
                    } else {
                        foreach ($challengeDuelQuestionsCreated as $challengeDuelQuestion) {
                            $answers = [];
                            //                            $currentQuestion = $challengeQuestionsRepository->find($challengeDuelQuestion->getId());
                            $currentQuestion = $challengeDuelQuestion->getQuestion();
                            \is_null($currentQuestion->getImage()) ? $imageRoute = null : $imageRoute = $this->settings->get('app.challenge_question_uploads_path') . $currentQuestion->getImage();

                            if ($challengeDuelQuestion->getQuestion()->getTranslations()->get($user->getLocale())) {
                                $questionText = $challengeDuelQuestion->getQuestion()->getTranslations()->get($user->getLocale())->getText();
                                foreach ($challengeDuelQuestion->getQuestion()->getChallengeAnswers() as $answer) {
                                    if ($answer->getTranslations()->get($user->getLocale())) {
                                        $answers[] = [
                                            'id' => $answer->getId(),
                                            'text' => $answer->getTranslations()->get($user->getLocale())->getText(),
                                        ];
                                    } else {
                                        $answers[] = [
                                            'id' => $answer->getId(),
                                            'text' => $answer->getText(),
                                        ];
                                    }
                                }
                            } else {
                                $questionText = $challengeDuelQuestion->getQuestion()->getText();
                                foreach ($challengeDuelQuestion->getQuestion()->getChallengeAnswers() as $answer) {
                                    $answers[] = [
                                        'id' => $answer->getId(),
                                        'text' => $answer->getText(),
                                    ];
                                }
                            }

                            $getQuestionsFinal[] = [
                                'question' => $currentQuestion,
                                'answers' => $answers,
                                'imageQuestion' => $imageRoute,
                                'questionText' => $questionText,
                            ];
                        }
                    }

                    $timeInSeg = $this->settings->get('app.challengeloops') * $this->settings->get('app.secondsPerQuestion');

                    $resultInfo = [
                        'duelInfo' => $challengeDuel,
                        'duelQuestions' => $getQuestionsFinal,
                        'totalTime' => $timeInSeg,
                    ];
                } else {
                    $code = Response::HTTP_INTERNAL_SERVER_ERROR;
                    $error = true;
                    $message = 'No tiene permiso para acceder a este duelo';
                }
            } else {
                $error = true;
                $resultInfo = [
                    'duelInfo' => 'No hay más duelos disponibles',
                    'duelQuestions' => [],
                    'totalTime' => null,
                ];
            }
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = $e->getMessage();
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $resultInfo : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['challengeduel', 'duelQuestions']]);
    }

    /**
     * @Rest\Post("/challenge/questions/set", name="api_challenge_setquestions")
     *
     * @SWG\Response(
     *     response=200,
     *     description="Crate or match duel"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="Error 404"
     * )
     * @SWG\Response(
     *     response=500,
     *     description="Error"
     * )
     *
     * @SWG\Tag(name="Getquestions")
     */
    public function setDuelQuestions(Request $request)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;
            // cargamos el usuario actual
            $userRepo = $this->em->getRepository(User::class);
            $user = $userRepo->findOneBy(['email' => $this->getUser()->getUsername()]);

            // cargamos las respuestas que vienen del front
            $content = json_decode($request->getContent());

            // buscamos el challegenduelquestion a la que pertenece el duelo
            $challengeDuelQuestionRepository = $this->em->getRepository(ChallengeDuelQuestions::class);
            $challengeAnswerRepository = $this->em->getRepository(ChallengeAnswers::class);

            // determinamos si el usuario actual es retador o retado
            $challengeDuelRepository = $this->em->getRepository(ChallengeDuel::class);
            $challengeDuel = $challengeDuelRepository->findBy(['id' => $content->idDuelo]);

            if ($challengeDuel[0]->getUser1() === $user) {
                $userPosition = self::USER_CHALLENGER;
            } else {
                $userPosition = self::USER_CHALLENGED;
            }

            // guardamos las preguntas
            $correctAnswers = 0;
            $timeCounter = 0;
            foreach ($content->answers as $answer) {
                if (!\is_null($answer)) {
                    $currrentAnswer = $challengeAnswerRepository->find($answer);
                    if ($currrentAnswer->getCorrect()) {
                        ++$correctAnswers;
                    }
                    $challengeDuelQuestion = $challengeDuelQuestionRepository->findBy(['duel' => $content->idDuelo, 'question' => $currrentAnswer->getPregunta()]);

                    if (self::USER_CHALLENGER === $userPosition) {
                        $challengeDuelQuestion[0]->setAnswerUser1Id($currrentAnswer)
                            ->setTimeUser1($content->time[$timeCounter]);
                    } else {
                        $challengeDuelQuestion[0]->setAnswerUser2Id($currrentAnswer)
                            ->setTimeUser2($content->time[$timeCounter]);
                    }
                    $this->em->persist($challengeDuelQuestion[0]);
                }
                ++$timeCounter;
            }

            // si el usuario es el segundo calculamos ganador
            if (self::USER_CHALLENGED === $userPosition) {
                $this->calculateWinner($challengeDuel[0], null, $correctAnswers);
            }

            $this->em->flush();
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = $e->getMessage();
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $correctAnswers : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['duelQuestions']]);
    }

    /**
     * @Rest\Get("/challenge/bot", name="api_challenge_bot")
     *
     * @SWG\Response(
     *     response=200,
     *     description="Bot running"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="Error 404"
     * )
     * @SWG\Response(
     *     response=500,
     *     description="Error"
     * )
     *
     * @SWG\Tag(name="Bot")
     */
    public function bot(): Response
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;

            $challengeDuelRepository = $this->em->getRepository(ChallengeDuel::class);
            $challengeDuelQuestionRepository = $this->em->getRepository(ChallengeDuelQuestions::class);
            $challengeAnswerRepository = $this->em->getRepository(ChallengeAnswers::class);
            $botsRepository = $this->em->getRepository(Bots::class);

            $botsList = $botsRepository->findAll();
            $temporalBotArray = [];
            /*
             * El bot debe encontrar todos los duelos que
             * 1 - El duelo no tenga ganador
             * 2 - Hayan pasado más de 24horas desde que se creo el duelo
             */
            $challengeDuels = $challengeDuelRepository->findBy(['winner' => null]);

            $now = new \DateTime();
            foreach ($challengeDuels as $challengeDuel) {
                $diff = $now->diff($challengeDuel->getCreatedAt());
                $timeOff = (int) $diff->format('%d');
                // Si no hay usuario 2 y todavía no ha pasado 1 día (24 horas) no se cierra
                if (0 == $timeOff && \is_null($challengeDuel->getUser2())) {
                    continue;
                }

                $challengeduelQuestions = $challengeDuelQuestionRepository->findBy(['duel' => $challengeDuel->getId()]);

                $selectedBot = array_rand($botsList, 1);
                $temporalBotArray[] = $selectedBot;
                $challengeDuel->setAssignedBot($botsRepository->find(['id' => $selectedBot + 1]));

                $answersArray = [];
                $duelLeft = false;
                $correctAnswers = 0;
                $duelLeftUser = 0;
                foreach ($challengeduelQuestions as $challengeduelQuestion) {
                    // aqui recorre las entradas de las respuestas a cada pregunta del duelo

                    // si abandona alguien no sigue
                    if (!$duelLeft) {
                        // todo mirar si el usuario 1 a respondido a las 3
                        // en caso de que no haya respondido a las 3 se le marcará como abandono de jugador 1
                        if (\is_null($challengeduelQuestion->getAnswerUser1Id())) {
                            // abandono jugador 1
                            $duelLeft = true;
                            $duelLeftUser = self::USER_CHALLENGER;
                        } elseif (!\is_null($challengeDuel->getUser2())) {
                            // todo si el usuario 1 a marcado las 3 miramos si el duelo tiene asignado al jugador 2
                            // si lo tiene asignado pero no ha respondido a todas se le marca como abandono de jugador 2
                            if (\is_null($challengeduelQuestion->getAnswerUser2Id())) {
                                // abandono jugador 2
                                $duelLeft = true;
                                $duelLeftUser = self::USER_CHALLENGED;
                            }
                        } else {
                            // todo Si no hay jugador 2 asignado y ha pasado mñas de 24horas entonces el bot responde a la pregunta
                            // una vez respondidas a las 3 preguntas calculará el ganador del encuentro y seteará winner
                            $botChooseAnswer = $this->saveChallengeDuelAnswer2($challengeduelQuestion);
                            $answersArray[] = $botChooseAnswer;
                            $currrentAnswer = $challengeAnswerRepository->find($botChooseAnswer);
                            if ($currrentAnswer->getCorrect()) {
                                ++$correctAnswers;
                            }
                            $duelLeftUser = 0;
                        }
                    }
                }
                $this->calculateWinner($challengeDuel, $duelLeftUser, $correctAnswers);
            }
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = $e->getMessage();
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK === $code ? $temporalBotArray : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['bot']]);
    }

    private function saveChallengeDuelAnswer2(ChallengeDuelQuestions $challengeQuestion): int
    {
        $challengeDuelQuestionRepository = $this->em->getRepository(ChallengeDuelQuestions::class);
        $challengeAnswerRepository = $this->em->getRepository(ChallengeAnswers::class);
        $allAnswers = $challengeDuelQuestionRepository->getAllAnswersWithSumAnswersByChallengeQuestionId($challengeQuestion->getQuestion()->getId());
        $totalAnswers = $challengeDuelQuestionRepository->getAllSumAnswerByChallengeQuestionId($challengeQuestion->getQuestion()->getId());

        $factorRandom = random_int(1, $totalAnswers[0]['totalAnswers']);

        $acum = 0;
        $selectedAnswerId = null;

        foreach ($allAnswers as $answer) {
            \is_null($acum) ? $acum = null : $acum += $answer['SumAnswers'];
            if ($factorRandom <= $acum) {
                $selectedAnswerId = $answer['answerId'];
                $acum = null;
            }
        }
        $challengeAnswer = $challengeAnswerRepository->find((int) $selectedAnswerId);
        $challengeQuestion->setAnswerUser2Id($challengeAnswer);
        $challengeQuestion->setTimeUser2(30);

        $this->em->persist($challengeQuestion);
        $this->em->flush();

        return (int) $selectedAnswerId;
    }

    private function calculateWinner(ChallengeDuel $challengeDuel, $duelLeftUser, $correctAnswers): void
    {
        $challengeDuelQuestionRepository = $this->em->getRepository(ChallengeDuelQuestions::class);
        $challengeAnswerRepository = $this->em->getRepository(ChallengeAnswers::class);
        $userPointsRepository = $this->em->getRepository(ChallengeUserPoints::class);

        $user1Points = $userPointsRepository->findOneBy(['challenge' => $challengeDuel->getChallenge(), 'user' => $challengeDuel->getUser1()]);
        if (\is_null($user1Points)) {
            $user1Points = new ChallengeUserPoints();
            $user1Points->setUser($challengeDuel->getUser1());
            $user1Points->setChallenge($challengeDuel->getChallenge());
        }

        $user2Points = $userPointsRepository->findOneBy(['challenge' => $challengeDuel->getChallenge(), 'user' => $challengeDuel->getUser2()]);
        if (\is_null($user2Points)) {
            $user2Points = new ChallengeUserPoints();
            $user2Points->setUser($challengeDuel->getUser2());
            $user2Points->setChallenge($challengeDuel->getChallenge());
        }

        switch ($duelLeftUser) {
            // abadono jugador 1
            case self::USER_CHALLENGER:
                $user1Points->setPoints($user1Points->getPoints() + $this->settings->get('app.pointsforleft'));
                $challengeDuel->setWinner(-5);
                $this->em->persist($user1Points);
                $this->em->persist($challengeDuel);
                break;
                // abandono jugador 2
            case self::USER_CHALLENGED:
                $user2Points->setPoints($user2Points->getPoints() + $this->settings->get('app.pointsforleft'));
                $user1Points = $userPointsRepository->findOneBy(['challenge' => $challengeDuel->getChallenge(), 'user' => $challengeDuel->getUser1()]);
                $user1Points->setPoints($user1Points->getPoints() + $this->settings->get('app.pointsforwin'));
                $challengeDuel->setWinner(-6);
                $this->em->persist($user2Points);
                $this->em->persist($user1Points);
                $this->em->persist($challengeDuel);
                break;

            case 0:
            case null:
                $correctAnswersUser1 = 0;
                $user1Time = 0;
                $user2Time = 0;
                $challengeDuelQuestion = $challengeDuelQuestionRepository->findBy(['duel' => $challengeDuel->getId()]);
                foreach ($challengeDuelQuestion as $item) {
                    $user1Time += $item->getTimeUser1();
                    $user2Time += $item->getTimeUser2();
                    $currrentAnswer = $challengeAnswerRepository->find($item->getAnswerUser1Id());
                    if ($currrentAnswer->getCorrect()) {
                        ++$correctAnswersUser1;
                    }
                }

                /*
                 * Casuistica de puntuaciones
                 *
                 * 1) user 1 > user 2 -> user 1 + 500, user 2 - 250;
                 * 2) user 1 < user 2 -> user 1 - 250, user 2 + 500;
                 * 3) user 1 = user 2 respuestas correctas > 0  -> user 1 + 250, user 2 + 250;
                 * 4) user 1 = user 2 respuestas correctas = 0  -> user 1 - 250, user 2 - 250;
                 * 5) abadono --
                 */

                /*
                 * Casusitica de ganadores(winner)
                 *  1 -> ganador jugador 1
                 *  2 -> ganador jugador 2
                 *  0 -> Empate con aciertos > 0
                 *  -1 -> Empate con aciertos = 0
                 *  -5 -> actuando bot -> hay abandono
                 *  -6 abaondolo jugardor 2 ganador 1
                 */

                //                if($duelLeftUser !== 0){
                //                    $user2Points = $userPointsRepository->findOneBy(['challenge'=> $challengeDuel->getChallenge(), 'user'=> $challengeDuel->getUser2()]);
                //                }

                // 1
                if ($correctAnswersUser1 > $correctAnswers) {
                    $challengeDuel->setWinner(self::USER_CHALLENGER);
                    $user1Points->setPoints($user1Points->getPoints() + $this->settings->get('app.pointsforwin'));
                    if (0 !== $duelLeftUser) {
                        $user2Points->setPoints($user2Points->getPoints() + $this->settings->get('app.pointsforloose'));
                    }
                }

                // 2 $this->settings->get()
                if ($correctAnswersUser1 < $correctAnswers) {
                    $challengeDuel->setWinner(self::USER_CHALLENGED);
                    $user1Points->setPoints($user1Points->getPoints() + $this->settings->get('app.pointsforloose'));
                    if (0 !== $duelLeftUser) {
                        $user2Points->setPoints($user2Points->getPoints() + $this->settings->get('app.pointsforwin'));
                    }
                }

                if ($correctAnswersUser1 === $correctAnswers) {
                    // 3
                    if ($correctAnswersUser1 > 0) {
                        if ($user1Time < $user2Time) {
                            $challengeDuel->setWinner(self::USER_CHALLENGER);
                            $user1Points->setPoints($user1Points->getPoints() + $this->settings->get('app.pointsforwin'));
                            if (0 !== $duelLeftUser) {
                                $user2Points->setPoints($user2Points->getPoints() + $this->settings->get('app.pointsforloose'));
                            }
                        } elseif ($user1Time > $user2Time) {
                            $challengeDuel->setWinner(self::USER_CHALLENGED);
                            $user1Points->setPoints($user1Points->getPoints() + $this->settings->get('app.pointsforloose'));
                            if (0 !== $duelLeftUser) {
                                $user2Points->setPoints($user2Points->getPoints() + $this->settings->get('app.pointsforwin'));
                            }
                        } else {
                            $challengeDuel->setWinner(0);
                            $user1Points->setPoints($user1Points->getPoints() + $this->settings->get('app.pointsfortie'));
                            if (0 !== $duelLeftUser) {
                                $user2Points->setPoints($user2Points->getPoints() + $this->settings->get('app.pointsfortie'));
                            }
                        }
                    } else {
                        // 4
                        $challengeDuel->setWinner(-1);
                        $user1Points->setPoints($user1Points->getPoints() + $this->settings->get('app.pointsfortiewithoutcorrects'));
                        if (0 !== $duelLeftUser) {
                            $user2Points->setPoints($user2Points->getPoints() + $this->settings->get('app.pointsfortiewithoutcorrects'));
                        }
                    }
                }
                $this->em->persist($user1Points);
                if (0 !== $duelLeftUser) {
                    $this->em->persist($user2Points);
                }
                $this->em->persist($challengeDuel);
                break;
        }
        $this->em->flush();
    }

    private function getPoints(): array
    {
        return [
            'win' => $this->settings->get('app.pointsforwin'),
            'loose' => $this->settings->get('app.pointsforloose'),
            'tie' => $this->settings->get('app.pointsfortie'),
            'tiewithoutcorrects' => $this->settings->get('app.pointsfortiewithoutcorrects'),
            'left' => $this->settings->get('app.pointsforleft'),
        ];
    }
}
