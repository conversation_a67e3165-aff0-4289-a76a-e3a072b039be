<?php

declare(strict_types=1);

namespace App\Campus\Controller\General;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\Scorm;
use App\Enum\ChapterContent;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\ScormRepository;
use App\Repository\UserCourseChapterRepository;
use App\Repository\UserCourseRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Route("/api")
 */
class ApiScormController extends ApiBaseController
{
    protected $em;

    /**
     * ApiController constructor.
     */
    public function __construct(LoggerInterface $logger, UserRepository $userRepository, CourseRepository $courseRepository, AnnouncementRepository $announcementRepository, EntityManagerInterface $em, TranslatorInterface $translator, SettingsService $settingsService)
    {
        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settingsService);
        $this->em = $em;
    }

    /**
     * @Rest\Get("/scorm/{id}/start", name="api_scorm_init")
     *
     * @return Response
     */
    public function scormInit(Chapter $chapter, UserCourseRepository $userCourseRepository, UserCourseChapterRepository $userCourseChapterRepository, ScormRepository $scormRepository)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;
            $message = 'Scorm correctly started';

            $course = $chapter->getCourse();

            if (!$this->checkCourseAccess($course)) {
                $code = Response::HTTP_UNAUTHORIZED;
                $error = true;
                $message = 'The user has no access to this course';
            } else {
                if (ChapterContent::SCORM_TYPE === $chapter->getType()->getId()) {
                    $scorm = $scormRepository->findOneBy([
                        'chapter' => $chapter,
                    ]);

                    $this->logger->error('Menu ' . $this->createMenu($scorm));

                    $user = $this->userRepository->findOneBy(['email' => $this->getUser()->getUsername()]);
                    $userCourse = $userCourseRepository->findOneBy([
                        'user' => $user,
                        'course' => $course,
                        'announcement' => null,
                    ]);

                    $userCourseChapter = $userCourseChapterRepository->findOneBy([
                        'userCourse' => $userCourse, 
                        'chapter' => $chapter,
                    ]);

                    $data = [
                        'scorm' => $scorm,
                        'chapter' => $userCourseChapter,
                    ];
                } else {
                    $code = Response::HTTP_UNAUTHORIZED;
                    $error = true;
                    $message = 'The Chapter is not a SCORM package';
                }
            }
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = "An error has occurred trying to start chapter: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $data : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['scorm', 'progress']]);
    }

    /**
     * @Rest\Post("/scorm/{id}/setvalue", name="api_scorm_setvalue")
     *
     * @return Response
     */
    public function scormSetValue(Chapter $chapter, Request $request, UserCourseRepository $userCourseRepository, UserCourseChapterRepository $userCourseChapterRepository)
    {
        
        try {
            $code = Response::HTTP_OK;
            $error = false;
            $message = 'Scorm correctly started';

            $course = $chapter->getCourse();

            if (!$this->checkCourseAccess($course)) {
                $code = Response::HTTP_UNAUTHORIZED;
                $error = true;
                $message = 'The user has no access to this course';
            } else {
                if (ChapterContent::SCORM_TYPE === $chapter->getType()->getId()) {
                    $user = $this->userRepository->findOneBy(['email' => $this->getUser()->getUsername()]);
                    $userCourse = $userCourseRepository->findOneBy([
                        'user' => $user,
                        'course' => $course,
                        'announcement' => null,
                    ]);

                    $userCourseChapter = $userCourseChapterRepository->findOneBy([
                        'userCourse' => $userCourse,
                        'chapter' => $chapter,
                    ]);

                    $data = $userCourseChapter->getData();

                    $params = json_decode($request->getContent(), true);

                    $data['scorm'][$params['param']] = $params['value'];
                    $userCourseChapter->setData($data);

                    $scorm = $scormRepository->findOneBy([
                        'chapter' => $chapter,
                    ]);

                    $rawScore = $scorm->getRawScore();
                    if (!isset($rawScore) || (\intval($rawScore) < 0) || (\intval($rawScore) > 100) ){
                        $rawScore = Scorm::SCORM_SCORE_RAW_DEFAULT_VALUE;
                    }

                    /* if($params['param'] === Scorm::SCORM_STATUS && $params['value'] === Scorm::SCORM_STATUS_PASSED) */
                    if (
                        (Scorm::SCORM_STATUS === $params['param'] && Scorm::SCORM_STATUS_COMPLETE === $params['value'])
                        || (Scorm::SCORM_STATUS === $params['param'] && Scorm::SCORM_STATUS_COMPLETED === $params['value'])
                        || (Scorm::SCORM_STATUS === $params['param'] && Scorm::SCORM_STATUS_PASSED === $params['value'])
                        || (Scorm::SCORM_COMPLETION === $params['param'] && Scorm::SCORM_STATUS_COMPLETED === $params['value'])
                        || (Scorm::SCORM_CORE_COMPLETION === $params['param'] && $rawScore <= \intval($params['value']))
                    ) {
                        $userCourseChapter->setFinishedAt(new \DateTime());

                        // checking if all chapters are finished to mark the course as finished
                        $chapersFinished = 0;
                        foreach ($userCourse->getChapters() as $chapter) {
                            if (!\is_null($chapter->getFinishedAt())) {
                                ++$chapersFinished;
                            }
                        }
                        if (\count($course->getChapters()) == $chapersFinished) {
                            $userCourse->setFinishedAt(new \DateTime());
                            $this->em->persist($userCourse);
                        }
                    }
                    $this->em->persist($userCourseChapter);

                    $this->em->flush();

                    if (isset($data->scorm)) {
                        $data = $data->scorm;
                    } else {
                        $data = null;
                    }
                } else {
                    $code = Response::HTTP_UNAUTHORIZED;
                    $error = true;
                    $message = 'The Chapter is not a SCORM package';
                }
            }
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = "An error has occurred trying to start chapter: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $data : $message,
        ];

        return $this->sendResponse($response);
    }

    /**
     * Recibe un objeto Scorm y devuelve el menu ya en html del mismo.
     *
     * @return string
     */
    private function createMenu(Scorm $scorm)
    {
        $menus = [];
        if ($scorm->hasManifest()) {
            $manifest = simplexml_load_file($scorm->getRoute() . 'imsmanifest.xml');
            $namespaces = $manifest->getDocNamespaces();
            $manifest->registerXPathNamespace('a', $namespaces['']);

            $organizations = $manifest->organizations;
            foreach ($organizations->organization as $organization) {
                $menu = [
                    'title' => $organization->title,
                    'submenu' => $this->getItems($organization, $manifest),
                ];

                $this->logger->error($organization->title);

                $menus[] = $menu;
            }
        }

        return $this->renderView('scorm/menu.html.twig', [
            'scorm' => $scorm,
            'menus' => $menus,
        ]);
    }

    /**
     * Recibe el XML del nodo padre completo y devuelve el árbol del items en un array.
     *
     * @return array
     */
    private function getItems($xml, $manifest)
    {
        $items = [];

        foreach ($xml->item as $item) {
            if (isset($item['identifierref'])) {
                $resource = $manifest->xpath('//a:resource[@identifier="' . $item['identifierref'] . '"]');
                $href = $resource[0]['href'];
                if (isset($item['parameters'])) {
                    $href .= $item['parameters'];
                }
            }

            $item = [
                'title' => $item->title,
                'href' => (isset($href)) ? $href : '',
                'submenu' => $this->getItems($item, $manifest),
            ];

            $items[] = $item;
        }

        return $items;
    }
}
