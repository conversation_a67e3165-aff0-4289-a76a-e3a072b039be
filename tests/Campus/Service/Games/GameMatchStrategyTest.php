<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\GameStrategyInterface;
use App\Campus\Games\MemMatch;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class GameMatchStrategyTest extends TestGameStrategyInterface
{
    public function getStrategy(): GameStrategyInterface
    {
        return new MemMatch(
            $this->createMock(EntityManagerInterface::class),
            $this->createMock(SettingsService::class),
            $this->createMock(RequestStack::class)
        );
    }

    public static function getValidData(): \Generator
    {
        $chapter = new Chapter();
        $chapterType = new ChapterType();
        $chapterType->setCode('999');
        $chapterType->setPercentageCompleted('0.75');
        $chapter->setType($chapterType);

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 10,
                'correct' => false,
                'cards' => [
                    0 => -79,
                    1 => 80,
                ],
            ],
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => false,
                'cards' => [
                    0 => -80,
                    1 => 80,
                ],
            ],
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => false,
                'cards' => [
                    0 => 80,
                    1 => -80,
                ],
            ],
        ];

        $attempts = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 10,
                'correct' => false,
                'cards' => [
                    0 => -79,
                    1 => 80,
                ],
            ],
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 10,
                'correct' => false,
                'cards' => [
                    0 => -80,
                    1 => 80,
                ],
            ],
        ];

        yield 'result ko, no chapter passed' => [
            'data' => ['answers' => $answers,
                'holes' => 2,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => null,
            'expectedPoints' => null,
        ];

        yield 'result ko, no total time passed' => [
            'data' => ['answers' => $answers,
                'holes' => 2,
                'timeTotal' => null,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no answers passed' => [
            'data' => ['answers' => null,
                'holes' => 2,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko No correct answers.' => [
            'data' => ['answers' => $answers,
                'holes' => 2,
                'totalQuestions' => 2,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 10,
                'correct' => false,
                'cards' => [
                    0 => -79,
                    1 => 80,
                ],
            ],
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
                'cards' => [
                    0 => -80,
                    1 => 80,
                ],
            ],
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 4,
                'correct' => true,
                'cards' => [
                    0 => 80,
                    1 => -80,
                ],
            ],
        ];

        yield 'result ok. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'holes' => 2,
                'totalQuestions' => 2,
                'timeTotal' => 30,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.87,
        ];

        yield 'result ok with less time. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'holes' => 2,
                'totalQuestions' => 2,
                'timeTotal' => 15,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.85,
        ];
    }
}
