<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Criteria;

use App\V2\Domain\Shared\Criteria\Pagination;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PaginationTest extends TestCase
{
    /**
     * Test that the constructor sets the page and limit correctly.
     */
    #[DataProvider('paginationProvider')]
    public function testConstructor(int $page, int $limit, int $expectedOffset): void
    {
        // Act
        $pagination = new Pagination($page, $limit);

        // Assert
        $this->assertSame($page, $pagination->page());
        $this->assertSame($limit, $pagination->limit());
        $this->assertSame($expectedOffset, $pagination->offset());
    }

    /**
     * Test that page returns the correct page.
     */
    public function testPage(): void
    {
        // Arrange
        $pagination = new Pagination(2, 10);

        // Act
        $result = $pagination->page();

        // Assert
        $this->assertSame(2, $result);
    }

    /**
     * Test that limit returns the correct limit.
     */
    public function testLimit(): void
    {
        // Arrange
        $pagination = new Pagination(2, 10);

        // Act
        $result = $pagination->limit();

        // Assert
        $this->assertSame(10, $result);
    }

    /**
     * Test that offset returns the correct offset.
     */
    public function testOffset(): void
    {
        // Arrange
        $pagination = new Pagination(2, 10);

        // Act
        $result = $pagination->offset();

        // Assert
        $this->assertSame(10, $result);
    }

    /**
     * Data provider for testConstructor.
     */
    public static function paginationProvider(): \Generator
    {
        yield 'page 1 limit 10' => [
            'page' => 1,
            'limit' => 10,
            'expectedOffset' => 0,
        ];

        yield 'page 2 limit 10' => [
            'page' => 2,
            'limit' => 10,
            'expectedOffset' => 10,
        ];

        yield 'page 3 limit 15' => [
            'page' => 3,
            'limit' => 15,
            'expectedOffset' => 30,
        ];

        yield 'page 0 limit 10' => [
            'page' => 0,
            'limit' => 10,
            'expectedOffset' => -10,
        ];

        yield 'page 1 limit 0' => [
            'page' => 1,
            'limit' => 0,
            'expectedOffset' => 0,
        ];
    }
}
