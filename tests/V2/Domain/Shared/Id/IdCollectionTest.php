<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Id;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;

class IdCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new IdCollection($items);
    }

    protected function getExpectedType(): string
    {
        return Id::class;
    }

    protected function getItem(): object
    {
        return new Id(33);
    }
}
