<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\Exceptions\LtiException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Infrastructure\LTI\OpenSSLKeyProvider;
use PHPUnit\Framework\TestCase;

class OpenSSLKeyProviderTest extends TestCase
{
    /**
     * @throws InfrastructureException
     * @throws LtiException
     */
    public function testKeyGeneration(): void
    {
        $ltiKeysDir = __DIR__ . '/../../../../var/tmp/lti1p3/';
        $provider = new OpenSSLKeyProvider(
            ltiKeysDir: $ltiKeysDir,
        );

        $provider->generateKeys();

        $this->assertEquals('file://' . $ltiKeysDir . 'public.key', $provider->getPublicKeyFile());
        $this->assertEquals('file://' . $ltiKeysDir . 'private.key', $provider->getPrivateKeyFile());
    }
}
