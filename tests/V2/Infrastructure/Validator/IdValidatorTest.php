<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator;

use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class IdValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('provideValidId')]
    public function testValidId(
        int $id
    ) {
        $this->expectNotToPerformAssertions();
        IdValidator::validateId($id);
    }

    public static function provideValidId(): \Generator
    {
        yield 'valid int id' => [
            'id' => 1,
        ];

        yield 'other valid int id' => [
            'id' => 9999,
        ];
    }

    #[DataProvider('provideInvalidId')]
    public function testInvalidId(int|string $id, array $violations): void
    {
        try {
            IdValidator::validateId($id);
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function provideInvalidId(): \Generator
    {
        yield 'id equal to 0' => [
            'id' => 0,
            'violations' => [
                '[id]' => 'id must be greater than 0.',
            ],
        ];

        yield 'id negative' => [
            'id' => -1000,
            'violations' => [
                '[id]' => 'id must be greater than 0.',
            ],
        ];
    }
}
