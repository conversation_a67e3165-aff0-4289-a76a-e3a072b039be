import fs from 'fs'
import { fileURLToPath, URL } from 'node:url'

async function importFavicon(source = '', isDefault = false) {
  const faviconSource = fileURLToPath(new URL(source, import.meta.url))
  fs.readFile(faviconSource, async (error, data) => {
    if (error) {
      if (isDefault) return console.log('❌  No se encontró el favicon')
      return importFavicon('../../contexts/shared/assets/images/favicon.ico', true)
    }

    const destination = fileURLToPath(new URL(`../../../public/favicon.ico`, import.meta.url))
    await fs.writeFile(destination, data, (error) => {
      if (error) return console.log('❌  Error al copiar el favicon')
      return console.log(
        '✅  ' +
          (isDefault ? 'Se ha copiado el favicon por defecto' : 'Se ha copiado correctamente el favicon del cliente')
      )
    })
  })
}

const origin = !!process.argv[2] && process.argv[2] !== 'default' ? `clients/${process.argv[2]}` : 'contexts'
importFavicon(`../../${origin}/shared/assets/images/favicon.ico`).catch(() => console.log('❌  Build error'))
