import { initValidations } from '@/core/router/guards.js'
import { LoaderService } from '@/core/services/loader.service.js'
import { ROUTES_WITHOUT_FOOTER, ROUTES_WITHOUT_HEADER } from '@/core/constants/router.constants.js'

function routeFactory(path = '', name = '', view = '', meta = {}) {
  const app = LoaderService.importView(view)
  const withHeader = $settings.SHOW_HEADER && !ROUTES_WITHOUT_HEADER.includes(name)
  const withFooter = $settings.SHOW_FOOTER && !ROUTES_WITHOUT_FOOTER.includes(name)

  return {
    path,
    name,
    meta: {
      isPublic: false,
      requiresAuth: false,
      withHeader,
      withFooter,
      ...meta,
    },
    components: {
      default: () => app,
      ...(withHeader ? { header: () => LoaderService.importComponent('AppHeader') } : {}),
      ...(withFooter ? { footer: () => LoaderService.importComponent('AppFooter') } : {}),
    },
    beforeEnter: initValidations,
  }
}

export const routes = [
  routeFactory('/login', 'login', '/LoginView'),
  routeFactory('/users', 'users', '/UserHomeView', { requiresAuth: true }),
  routeFactory('/user-form/:id(\\d+)?', 'user-form', '/UserFormView', { requiresAuth: true }),
  routeFactory('/user-profile/:id(\\d+)', 'user-profile', '/UserProfileView', { requiresAuth: true }),
]
