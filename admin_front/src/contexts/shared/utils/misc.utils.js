export function bytesToText(bytes = 0) {
  if (!bytes) return '-'
  const sizes = ['B', 'Kb', 'Mb', 'Gb', 'Tb', 'Pb']
  const index = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${parseFloat((bytes / Math.pow(1024, index)).toFixed(2))} ${sizes[index]}`
}

export function jsonToString(json, defaultValue = {}) {
  try {
    return JSON.parse(json)
  } catch (e) {
    return defaultValue
  }
}
