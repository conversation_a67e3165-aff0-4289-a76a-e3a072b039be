import { defineStore } from 'pinia'
import { ref } from 'vue'
import ApiService from '@/core/services/api.service.js'
import { USER_API_ROUTES } from '@/contexts/users/constants/users.constants.js'
import { toast } from 'vue3-toastify'
import { UserFormModel } from '@/contexts/users/models/userForm.model.js'
import { useI18n } from 'vue-i18n'
import { UserFormExtraFieldModel } from '@/contexts/users/models/userFormExtraField.model.js'

export const userFormStore = defineStore('userFormStore', () => {
  const userData = ref(new UserFormModel())
  const i18n = useI18n()
  async function loadUserData(id = 0) {
    if (!id) {
      userData.value = new UserFormModel()
      return null
    }
    const { error, data } = await ApiService.get(USER_API_ROUTES.FORM.INIT_DATA.replace(':id', `${id}`))
    if (error) return toast.error(error)
    userData.value = new UserFormModel(data)
  }

  const successSave = ref(false)
  async function createUser(payload = {}) {
    successSave.value = false
    const { error } = await ApiService.post(USER_API_ROUTES.FORM.NEW_USER, payload)
    if (error) return toast.error(error)
    toast.success(i18n.t('CATALOG.SAVED'))
    successSave.value = true
  }

  async function updateUser(id = 0, payload = {}) {
    successSave.value = false
    const { error } = await ApiService.post(USER_API_ROUTES.FORM.UPDATE_USER.replace(':id', `${id}`), payload)
    if (error) return toast.error(error)
    toast.success(i18n.t('CATALOG.SAVED'))
    successSave.value = true
  }

  const extraData = ref([])
  async function loadExtraData() {
    extraData.value = []
    const { error, data } = await ApiService.get(USER_API_ROUTES.FORM.EXTRA_FIELDS)
    if (error) return toast.error(error)
    extraData.value = data.map((field) => new UserFormExtraFieldModel(field))
  }

  return {
    userData,
    successSave,
    extraData,
    loadUserData,
    createUser,
    updateUser,
    loadExtraData,
  }
})
