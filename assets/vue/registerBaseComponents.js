import Vue from 'vue'
import upperFirst from 'lodash/upperFirst'
import camelCase from 'lodash/camelCase'

// Busca todos los componentes base en la carpeta ./components/base
const requireComponent = require.context(
  './base',
  false,
  /Base[A-Z]\w+\.(vue|js)$/
)

requireComponent.keys().forEach(fileName => {
  // Obtiene la configuración del componente
  const componentConfig = requireComponent(fileName)

  // Convierte el nombre del archivo en PascalCase,
  // eliminando la extensión del archivo
  const componentName = upperFirst(
    camelCase(
      fileName
        .split('/')
        .pop()
        .replace(/\.\w+$/, '')
    )
  )

  // Registra el componente globalmente
  Vue.component(
    componentName,
    componentConfig.default || componentConfig
  )
})
