<script>
import Spinner from "../../../admin/components/base/Spinner.vue";
import Pagination from "../../../admin/components/Pagination.vue";
import {get, sync} from "vuex-pathify";

import Multiselect from "vue-multiselect";

export default {
  name: "TypeCourse",
  components: {Spinner, Pagination, Multiselect},
  data() {
    return {
      locale: 'es',
      paginatedList: [],
      pageSize: 12,
      stepsSelects:[],
      allStepsSelects:[],
      translations: [],
      defaultValue: { id: '', name: '' },
      groups: [],
      filterTypeCourse: [],
    }
  },
  computed: {
    loading: get('catalogModule/loading'),
    catalogs: sync('catalogModule/catalogs'),
  },
  mounted() {
    this.stepsSelects = this.catalogs.slice(0, this.pageSize);
  },

  async created() { 
    const data = await this.$store.dispatch('catalogModule/load', '/admin/TypeCourse-AnnouncementStepCreation/all'); 
    this.allStepsSelects = this.filterTypeCourse = data.data;  
    this.selectelements();
  },
  methods: {
    changeActiveStatus(index) {

      const originalValue = this.stepsSelects[index].active;
      const value = this.stepsSelects[index];

      this.$store.dispatch('catalogModule/changeActiveState', {
        endpoint: `/admin/TypeCourse-AnnouncementStepCreation/${value.id}/active`,
        data: { id: value.id, active: value.active }
      })
      .then((response) => {
        if (response.error) {
          this.stepsSelects[index].active = false;
          this.$toast.error('Error: You must first activate the corresponding configuration at the platform level.');
        }
        if(value.id === 3 && value.active === true){
          this.$toast.error('Debemos ir "Configuración de cliente convocatoria/BONIFICACION/" pasarlo a activo. de ' +
              'igual manera la configuración de seccion "Curso bonificado"');
        }
      })
      .catch((error) => {
        this.stepsSelects[index].active = !originalValue;
        this.$toast.error('An unexpected error occurred.');
      });
    },
    selectelements(){  
      let i=1;
      let nameTypecourse=[];

      this.allStepsSelects.forEach(item => {
          if(!nameTypecourse.includes(item.typeCourseName)) 
            nameTypecourse.push(item.typeCourseName);
        });
        
        nameTypecourse.forEach(item => {        
          let element = {
            id:null,
            name:null,
          };
          element.id=i;
          element.name = item;
          this.groups.push(element);
          i++;
        });
    },
    selectFilter(){
      this.filterTypeCourse = [];
      
      this.allStepsSelects.forEach((item) => {
        if(item.typeCourseName=== this.defaultValue.name) this.filterTypeCourse.push(item);      
      });

      this.stepsSelects = this.filterTypeCourse; 
    },
  }
}
</script>

<template>
  <div>
    <div class="d-flex align-items-start justify-content-end">   
      <div class="col-12">
        <multiselect v-model="defaultValue" 
          :options="groups" 
          placeholder="Select one" 
          label="name" 
          track-by="name"
          @input="selectFilter"
        >
        </multiselect> 
      </div> 
    </div>
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>
    <table class="table table-condensed mt-3" v-else>
      <thead>
      <tr>
        <th></th>
        <th>{{ $t('NAME') }}</th>
        <th>{{ $t('COURSE.TYPE_COURSE') }}</th>
        <th>{{ $t('CATALOG.TYPE_COURSE.DENOMINATION') }}</th>
        <th>{{ $t('ACTIVE') }}</th>
        <th>{{ $t('POSITION') }}</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(c, index) in stepsSelects" :key="c.id">
        <td></td>
        <td>{{ c.name }}</td>
        <td>{{ c.typeCourseName }}</td>
        <td>{{ c.denomination }}</td>
        <td>
          <BaseSwitch :tag="`switcher-TypeCourse-AnnouncementStepCreation-${c.id}`" v-model="c.active" @change="changeActiveStatus(index)" />
        </td>
        <td>{{ c.position }}</td>
        <td>
          <div class="dropdown">
            <button class="btn btn-default" type="button" :id="`dropdown-menu-${c.id}`" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${c.id}`">
              <li><router-link class="dropdown-item" :to="{ name: 'TypeCourseAnnouncementStepCreationUpdate', params: {...$route.params, id: c.id} }">{{ $t('EDIT') }}</router-link></li>
            </ul>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  <div class="d-flex justify-content-between align-items-center flex-wrap footer pb-3">
    <p class="my-0 mx-sm-0 mx-auto"><b>{{ filterTypeCourse.length }}</b> {{ $t('INPUT.STEP') }}</p>
    <pagination
        v-show="pageSize < filterTypeCourse.length"
        :items="catalogs"
        :page-size="pageSize"
        @items-page="stepsSelects=$event"
        class="mx-sm-0 mx-auto"
    />
  </div>
  </div>
</template>

<style scoped lang="scss">

</style>
