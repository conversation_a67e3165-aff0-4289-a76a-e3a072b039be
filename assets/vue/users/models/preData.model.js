import { BaseFilterSelector } from './filters.model'

export class PreDataModel {
  constructor({ timezones = [], locales = [], companies = [], roles = [] } = {}) {
    this.timezones = (timezones || []).map((item, index) => new BaseFilterSelector({ key: `tzOption${index}`, value: item, text: item}))
    this.locales = (locales || []).map((item, index) => new BaseFilterSelector({ key: `localeOption${index}`, value: item.id, text: item.name }))
    this.companies = (companies || []).map((item, index) => new BaseFilterSelector({ key: `companyOption${index}`, value: item.id, text: item.name }))
    this.roles = (roles || []).map((item, index) => new BaseFilterSelector({ key: `roleOption${index}`, value: item.id, text: item.name }))
  }
}